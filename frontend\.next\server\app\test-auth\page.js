(()=>{var e={};e.id=580,e.ids=[580],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3037:(e,t,r)=>{Promise.resolve().then(r.bind(r,5497))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5478:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,tu:()=>d,zf:()=>a});let s={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},n=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],i={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function o(e){if(e?.code)return Object.values(s).includes(e.code);if(e?.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function a(e){let t="";return(t=e?.message?e.message:e?.error?e.error:"string"==typeof e?e:"操作失败，请稍后重试",o(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return i.default;if(e&&"string"==typeof e&&n.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return i.network;if(t.includes("timeout"))return i.timeout;if(t.includes("auth")||t.includes("token"))return i.auth;else if(t.includes("server")||t.includes("internal"))return i.server;else if(t.includes("database"))return i.database;else return i.unknown}return e}(t)}function d(e,t){let r=o(e);r&&t&&t();let s=o(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:e?.shouldRedirect===void 0||e.shouldRedirect}:{title:"操作失败",description:a(e),shouldRedirect:!1};return{isAuthError:r,message:s.description,shouldRedirect:s.shouldRedirect}}function l(e){return o(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:e?.message?.includes("卡密")?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:a(e),variant:"destructive"}}},5497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(687),n=r(3210),i=r(4934),o=r(5192),a=r(4426),d=r(9556),l=r(1702),u=r(5478);function c(){let[e,t]=(0,n.useState)(""),[r,c]=(0,n.useState)(!1),{toast:p}=(0,l.dj)(),h=async()=>{try{d.tC.setTokens("invalid_token","invalid_refresh_token","<EMAIL>");let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(s){console.error("测试错误:",s),t("捕获错误: "+s.message+" (code: "+(s.code||"无")+")");let{isAuthError:e,shouldRedirect:r}=(0,u.tu)(s,()=>{c(!0),p({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&r&&setTimeout(()=>{window.location.href="/login"},2e3)}},m=async()=>{try{d.tC.clearTokens();let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(s){console.error("测试错误:",s),t("捕获错误: "+s.message+" (code: "+(s.code||"无")+")");let{isAuthError:e,shouldRedirect:r}=(0,u.tu)(s,()=>{c(!0),p({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&r&&setTimeout(()=>{window.location.href="/login"},2e3)}};return(0,s.jsx)("div",{className:"container mx-auto p-4",children:(0,s.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"认证错误处理测试"})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.$,{onClick:h,variant:"outline",children:"测试无效Token"}),(0,s.jsx)(i.$,{onClick:m,variant:"outline",children:"测试无Token"})]}),e&&(0,s.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,s.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]}),r&&(0,s.jsxs)("div",{className:"p-4 bg-red-100 border border-red-300 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-red-800",children:"认证错误弹窗已触发!"}),(0,s.jsx)("p",{className:"text-red-600",children:"正在准备跳转到登录页面..."})]})]})]})})}},8125:(e,t,r)=>{Promise.resolve().then(r.bind(r,9255))},8543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(5239),n=r(8088),i=r(8170),o=r.n(i),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9255)),"D:\\myaitts-worker\\frontend\\app\\test-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\myaitts-worker\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["D:\\myaitts-worker\\frontend\\app\\test-auth\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts-worker\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts-worker\\frontend\\app\\test-auth\\page.tsx","default")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[995,773],()=>r(8543));module.exports=s})();