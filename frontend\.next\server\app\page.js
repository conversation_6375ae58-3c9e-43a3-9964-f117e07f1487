(()=>{var e={};e.id=974,e.ids=[974],e.modules={43:(e,t,r)=>{"use strict";r.d(t,{jH:()=>s});var n=r(3210);r(687);var o=n.createContext(void 0);function s(e){let t=n.useContext(o);return e||t||"ltr"}},83:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},275:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>m,routeModule:()=>u,tree:()=>c});var n=r(5239),o=r(8088),s=r(8170),a=r.n(s),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,597)),"D:\\myaitts-worker\\frontend\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\myaitts-worker\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],m=["D:\\myaitts-worker\\frontend\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts-worker\\\\frontend\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts-worker\\frontend\\app\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},850:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tc});var n=r(687),o=r(6180),s=r.n(o),a=r(3210),i=r(4934),l=r(5192);function c(e,[t,r]){return Math.min(r,Math.max(t,e))}var m=r(569),d=r(8599),u=r(1273),p=r(5551),b=r(43),g=r(3721),f=r(8853),x=r(4163),h=r(9510),y=["PageUp","PageDown"],w=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},k="Slider",[j,N,z]=(0,h.N)(k),[C,A]=(0,u.A)(k,[z]),[S,T]=C(k),$=a.forwardRef((e,t)=>{let{name:r,min:o=0,max:s=100,step:i=1,orientation:l="horizontal",disabled:d=!1,minStepsBetweenThumbs:u=0,defaultValue:b=[o],value:g,onValueChange:f=()=>{},onValueCommit:x=()=>{},inverted:h=!1,form:v,...k}=e,N=a.useRef(new Set),z=a.useRef(0),C="horizontal"===l,[A=[],T]=(0,p.i)({prop:g,defaultProp:b,onChange:e=>{let t=[...N.current];t[z.current]?.focus(),f(e)}}),$=a.useRef(A);function E(e,t,{commit:r}={commit:!1}){let n=(String(i).split(".")[1]||"").length,a=c(function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-o)/i)*i+o,n),[o,s]);T((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,a,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,u*i))return e;{z.current=n.indexOf(a);let t=String(n)!==String(e);return t&&r&&x(n),t?n:e}})}return(0,n.jsx)(S,{scope:e.__scopeSlider,name:r,disabled:d,min:o,max:s,valueIndexToChangeRef:z,thumbs:N.current,values:A,orientation:l,form:v,children:(0,n.jsx)(j.Provider,{scope:e.__scopeSlider,children:(0,n.jsx)(j.Slot,{scope:e.__scopeSlider,children:(0,n.jsx)(C?I:M,{"aria-disabled":d,"data-disabled":d?"":void 0,...k,ref:t,onPointerDown:(0,m.m)(k.onPointerDown,()=>{d||($.current=A)}),min:o,max:s,inverted:h,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(A,e);E(e,t)},onSlideMove:d?void 0:function(e){E(e,z.current)},onSlideEnd:d?void 0:function(){let e=$.current[z.current];A[z.current]!==e&&x(A)},onHomeKeyDown:()=>!d&&E(o,0,{commit:!0}),onEndKeyDown:()=>!d&&E(s,A.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!d){let r=y.includes(e.key)||e.shiftKey&&w.includes(e.key),n=z.current;E(A[n]+i*(r?10:1)*t,n,{commit:!0})}}})})})})});$.displayName=k;var[E,L]=C(k,{startEdge:"left",endEdge:"right",size:"width",direction:1}),I=a.forwardRef((e,t)=>{let{min:r,max:o,dir:s,inverted:i,onSlideStart:l,onSlideMove:c,onSlideEnd:m,onStepKeyDown:u,...p}=e,[g,f]=a.useState(null),x=(0,d.s)(t,e=>f(e)),h=a.useRef(void 0),y=(0,b.jH)(s),w="ltr"===y,k=w&&!i||!w&&i;function j(e){let t=h.current||g.getBoundingClientRect(),n=W([0,t.width],k?[r,o]:[o,r]);return h.current=t,n(e-t.left)}return(0,n.jsx)(E,{scope:e.__scopeSlider,startEdge:k?"left":"right",endEdge:k?"right":"left",direction:k?1:-1,size:"width",children:(0,n.jsx)(R,{dir:y,"data-orientation":"horizontal",...p,ref:x,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=j(e.clientX);l?.(t)},onSlideMove:e=>{let t=j(e.clientX);c?.(t)},onSlideEnd:()=>{h.current=void 0,m?.()},onStepKeyDown:e=>{let t=v[k?"from-left":"from-right"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),M=a.forwardRef((e,t)=>{let{min:r,max:o,inverted:s,onSlideStart:i,onSlideMove:l,onSlideEnd:c,onStepKeyDown:m,...u}=e,p=a.useRef(null),b=(0,d.s)(t,p),g=a.useRef(void 0),f=!s;function x(e){let t=g.current||p.current.getBoundingClientRect(),n=W([0,t.height],f?[o,r]:[r,o]);return g.current=t,n(e-t.top)}return(0,n.jsx)(E,{scope:e.__scopeSlider,startEdge:f?"bottom":"top",endEdge:f?"top":"bottom",size:"height",direction:f?1:-1,children:(0,n.jsx)(R,{"data-orientation":"vertical",...u,ref:b,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=x(e.clientY);i?.(t)},onSlideMove:e=>{let t=x(e.clientY);l?.(t)},onSlideEnd:()=>{g.current=void 0,c?.()},onStepKeyDown:e=>{let t=v[f?"from-bottom":"from-top"].includes(e.key);m?.({event:e,direction:t?-1:1})}})})}),R=a.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:o,onSlideMove:s,onSlideEnd:a,onHomeKeyDown:i,onEndKeyDown:l,onStepKeyDown:c,...d}=e,u=T(k,r);return(0,n.jsx)(x.sG.span,{...d,ref:t,onKeyDown:(0,m.m)(e.onKeyDown,e=>{"Home"===e.key?(i(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):y.concat(w).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:(0,m.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),u.thumbs.has(t)?t.focus():o(e)}),onPointerMove:(0,m.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&s(e)}),onPointerUp:(0,m.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),D="SliderTrack",P=a.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,s=T(D,r);return(0,n.jsx)(x.sG.span,{"data-disabled":s.disabled?"":void 0,"data-orientation":s.orientation,...o,ref:t})});P.displayName=D;var _="SliderRange",U=a.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,s=T(_,r),i=L(_,r),l=a.useRef(null),c=(0,d.s)(t,l),m=s.values.length,u=s.values.map(e=>K(e,s.min,s.max)),p=m>1?Math.min(...u):0,b=100-Math.max(...u);return(0,n.jsx)(x.sG.span,{"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,...o,ref:c,style:{...e.style,[i.startEdge]:p+"%",[i.endEdge]:b+"%"}})});U.displayName=_;var F="SliderThumb",O=a.forwardRef((e,t)=>{let r=N(e.__scopeSlider),[o,s]=a.useState(null),i=(0,d.s)(t,e=>s(e)),l=a.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,n.jsx)(B,{...e,ref:i,index:l})}),B=a.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:s,...i}=e,l=T(F,r),c=L(F,r),[u,p]=a.useState(null),b=(0,d.s)(t,e=>p(e)),g=!u||l.form||!!u.closest("form"),h=(0,f.X)(u),y=l.values[o],w=void 0===y?0:K(y,l.min,l.max),v=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,l.values.length),k=h?.[c.size],N=k?function(e,t,r){let n=e/2,o=W([0,50],[0,n]);return(n-o(t)*r)*r}(k,w,c.direction):0;return a.useEffect(()=>{if(u)return l.thumbs.add(u),()=>{l.thumbs.delete(u)}},[u,l.thumbs]),(0,n.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${w}% + ${N}px)`},children:[(0,n.jsx)(j.ItemSlot,{scope:e.__scopeSlider,children:(0,n.jsx)(x.sG.span,{role:"slider","aria-label":e["aria-label"]||v,"aria-valuemin":l.min,"aria-valuenow":y,"aria-valuemax":l.max,"aria-orientation":l.orientation,"data-orientation":l.orientation,"data-disabled":l.disabled?"":void 0,tabIndex:l.disabled?void 0:0,...i,ref:b,style:void 0===y?{display:"none"}:e.style,onFocus:(0,m.m)(e.onFocus,()=>{l.valueIndexToChangeRef.current=o})})}),g&&(0,n.jsx)(H,{name:s??(l.name?l.name+(l.values.length>1?"[]":""):void 0),form:l.form,value:y},o)]})});O.displayName=F;var H=e=>{let{value:t,...r}=e,o=a.useRef(null),s=(0,g.Z)(t);return a.useEffect(()=>{let e=o.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,n.jsx)("input",{style:{display:"none"},...r,ref:o,defaultValue:t})};function K(e,t,r){return c(100/(r-t)*(e-t),[0,100])}function W(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var G=r(6241);let q=a.forwardRef(({className:e,...t},r)=>(0,n.jsxs)($,{ref:r,className:(0,G.cn)("relative flex w-full touch-none select-none items-center",e),...t,children:[(0,n.jsx)(P,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,n.jsx)(U,{className:"absolute h-full bg-primary"})}),(0,n.jsx)(O,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));q.displayName=$.displayName;let V=e=>({getPosition:(0,a.useCallback)(()=>{let t=e.current;if(!t)return 0;let r=window.getSelection();if(r&&r.rangeCount>0){let e=r.getRangeAt(0),n=e.cloneRange();return n.selectNodeContents(t),n.setEnd(e.endContainer,e.endOffset),n.toString().length}return 0},[e]),setPosition:(0,a.useCallback)(t=>{let r=e.current;if(!r||t<0)return;let n=window.getSelection(),o=document.createRange(),s=0,a=!1;if(0===r.childNodes.length){r.focus();return}(function e(r){if(!a){if(r.nodeType===Node.TEXT_NODE){let e=s+r.length;t<=e?(o.setStart(r,t-s),o.setEnd(r,t-s),a=!0):s=e}else if(r.nodeType===Node.ELEMENT_NODE){for(let t of Array.from(r.childNodes))if(e(t),a)return}}})(r),a||(o.selectNodeContents(r),o.collapse(!1)),n&&(n.removeAllRanges(),n.addRange(o))},[e])}),Z=e=>e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\[([a-zA-Z\s]+)\]/g,'<span class="emotion-tag">[$1]</span>'):"",J=({value:e,onChange:t,onFocus:r,onBlur:o,placeholder:s,className:i,maxLength:l,onMaxLengthExceeded:c})=>{let m=(0,a.useRef)(null),d=(0,a.useRef)(e),{getPosition:u,setPosition:p}=V(m);(0,a.useEffect)(()=>{if(m.current&&e!==d.current){let t=u();m.current.innerHTML=Z(e),p(t)}d.current=e},[e,u,p]);let b=(0,a.useCallback)(()=>{if(m.current){let e=m.current.innerText;if(e.length>l){let r=e.substring(0,l),n=u();m.current.innerHTML=Z(r),p(Math.min(l,n)),c&&c(e.length,l),t(r);return}t(e)}},[t,l,e,u,p,c]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{children:`
        .emotion-tag {
          color: #8B5CF6; /* a nice purple color */
          font-weight: 600;
          background-color: rgba(139, 92, 246, 0.05);
          padding: 2px 1px;
          border-radius: 4px;
        }
      `}),(0,n.jsx)("div",{ref:m,contentEditable:!0,onInput:b,onFocus:r,onBlur:o,className:i,"data-placeholder":s,suppressContentEditableWarning:!0,style:{whiteSpace:"pre-wrap",wordWrap:"break-word"}})]})};var Y=r(8988),X=r(2688);let Q=(0,X.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var ee=r(3259),et=r(8869);let er=(0,X.A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var en=r(83),eo=r(5583),es=r(1312),ea=r(6085);let ei=(0,X.A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]),el=(0,X.A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),ec=(0,X.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),em=(0,X.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),ed=(0,X.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),eu=(0,X.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var ep=r(1860);let eb=(0,X.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),eg=(0,X.A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ef=(0,X.A)("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),ex=(0,X.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eh=(0,X.A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var ey=r(6349),ew=r(5778),ev=r(2597),ek=r(3861),ej=r(5336);let eN=(0,X.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),ez=(0,X.A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),eC=(0,X.A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),eA=(0,X.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),eS=(0,X.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var eT=r(7826);async function e$(e){try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),!0;{let t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let r=document.execCommand("copy");return document.body.removeChild(t),r}}catch(e){return console.error("复制失败:",e),!1}}function eE(e,t){let r=e.split("\n").map(e=>e.trim()).filter(e=>e.length>0),n=[],o=[],s=[],a=0,i=!1;if(0===r.length)return o.push("导入内容为空"),{validLines:n,errors:o,warnings:s};if(r.forEach((e,r)=>{let l=r+1;if(i){s.push(`📝 总字数已达4000字符上限，第${l}行及后续内容已被忽略`);return}let c=e.match(/^(.+?)@(.+)$/);if(!c){o.push(`第${l}行格式错误：${e.substring(0,50)}${e.length>50?"...":""}`);return}let[,m,d]=c,u=m.trim(),p=d.trim();if(!u){o.push(`第${l}行声音名称为空`);return}if(!p){o.push(`第${l}行对话内容为空`);return}let b=p;if(p.length>1e3&&(b=p.substring(0,1e3),s.push(`✂️ 第${l}行文本过长(${p.length}字符)，已自动截断到1000字符`)),a+b.length>4e3){let e=4e3-a;if(e>0)b=b.substring(0,e),s.push(`📊 第${l}行因总字数限制(4000字符)被截断，保留前${e}字符`),a=4e3;else{s.push(`📊 第${l}行及后续内容因总字数限制(4000字符)被忽略`),i=!0;return}i=!0}else a+=b.length;let g=function(e,t){let r=e.toLowerCase().trim();if(!r)return null;let n=t.find(e=>e.name.toLowerCase()===r);if(n||(n=t.find(e=>e.name.toLowerCase().includes(r)||r.includes(e.name.toLowerCase()))))return n;let o=r.replace(/\s+/g,"");return(n=t.find(e=>{let t=e.name.toLowerCase().replace(/\s+/g,"");return t===o||t.includes(o)||o.includes(t)}))||o.length<=3&&(n=t.find(e=>e.name.toLowerCase().split(/\s+/).map(e=>e.charAt(0)).join("")===o))?n:null}(u,t);g?n.push({id:Date.now()+Math.random()+r,voice:g.id,text:b}):(s.push(`第${l}行声音"${u}"不存在，将使用默认声音`),n.push({id:Date.now()+Math.random()+r,voice:t[0]?.id||"",text:b}))}),n.length>0){let e=n.reduce((e,t)=>e+t.text.length,0);e>=4e3?s.push(`📈 导入完成：共${n.length}行对话，总字数${e}字符（已达上限）`):s.some(e=>e.includes("截断")||e.includes("忽略"))&&s.push(`📈 导入完成：共${n.length}行对话，总字数${e}字符（含截断处理）`)}return{validLines:n,errors:o,warnings:s}}let eL=({dialogueLines:e,voices:t,onImport:r,className:o=""})=>{let[s,l]=(0,a.useState)({showImportDialog:!1,showStatsDialog:!1,importText:"",importMode:"replace",importPreview:[],importErrors:[],importWarnings:[],isImporting:!1,isCopying:!1,copySuccess:!1,showSample:!1}),c=(0,a.useCallback)(async()=>{if(!s.isCopying){l(e=>({...e,isCopying:!0}));try{let r=function(e,t){let r=e.filter(e=>e.text.trim());if(0===r.length)throw Error("没有可复制的对话内容");return r.map(e=>{let r=t.find(t=>t.id===e.voice),n=r?.name||"未知声音";return`${n}@${e.text.trim()}`}).join("\n")}(e,t);if(await e$(r))l(e=>({...e,copySuccess:!0})),setTimeout(()=>{l(e=>({...e,copySuccess:!1}))},2e3);else throw Error("复制失败")}catch(e){console.error("复制失败:",e)}finally{l(e=>({...e,isCopying:!1}))}}},[e,t,s.isCopying]),m=(0,a.useCallback)(()=>{let e=function(e){let t=e.slice(0,3),r=["你好！今天过得怎么样？","我今天过得很好，谢谢你的关心！","那真是太好了，有什么特别的事情吗？"];return t.map((e,t)=>`${e.name}@${r[t]||"这是一个示例对话。"}`).join("\n")}(t);l(t=>({...t,importText:e,showSample:!0}));let{validLines:r,errors:n,warnings:o}=eE(e,t);l(e=>({...e,importPreview:r,importErrors:n,importWarnings:o}))},[t]),d=(0,a.useCallback)(()=>{l(e=>({...e,showStatsDialog:!0}))},[]),u=(0,a.useCallback)(e=>{if(l(t=>({...t,importText:e,showSample:!1})),e.trim()){let{validLines:r,errors:n,warnings:o}=eE(e,t);l(e=>({...e,importPreview:r,importErrors:n,importWarnings:o}))}else l(e=>({...e,importPreview:[],importErrors:[],importWarnings:[]}))},[t]),p=(0,a.useCallback)(async()=>{if(0!==s.importPreview.length){l(e=>({...e,isImporting:!0}));try{await r(s.importPreview,s.importMode),l(e=>({...e,showImportDialog:!1,importText:"",importPreview:[],importErrors:[],importWarnings:[],isImporting:!1,showSample:!1}))}catch(e){console.error("导入失败:",e),l(e=>({...e,isImporting:!1}))}}},[s.importPreview,s.importMode,r]),b=e.some(e=>e.text.trim());return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:`flex items-center gap-3 ${o}`,children:[(0,n.jsxs)("button",{onClick:c,disabled:s.isCopying||!b,className:`relative overflow-hidden px-4 py-2 text-xs font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group ${s.isCopying||!b?"text-gray-400 bg-gray-100 cursor-not-allowed opacity-50":"text-white bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm"}`,title:b?"复制所有对话到剪贴板":"没有可复制的对话内容",children:[!s.isCopying&&!!b&&(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),s.isCopying?(0,n.jsx)("div",{className:"w-3.5 h-3.5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):s.copySuccess?(0,n.jsx)(ej.A,{className:"w-3.5 h-3.5 text-green-400"}):(0,n.jsx)(ez,{className:"w-3.5 h-3.5"}),(0,n.jsx)("span",{className:"relative z-10",children:s.isCopying?"复制中...":s.copySuccess?"已复制":"复制对话"})]}),(0,n.jsxs)("button",{onClick:()=>l(e=>({...e,showImportDialog:!0})),className:"relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group",title:"从剪贴板导入批量对话",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,n.jsx)(eC,{className:"w-3.5 h-3.5 relative z-10"}),(0,n.jsx)("span",{className:"relative z-10",children:"导入对话"})]}),b&&(0,n.jsxs)("button",{onClick:d,className:"relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-purple-500 via-violet-600 to-purple-600 hover:from-purple-600 hover:via-violet-700 hover:to-purple-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group",title:"查看对话统计信息",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,n.jsx)(eA,{className:"w-3.5 h-3.5 relative z-10"}),(0,n.jsx)("span",{className:"relative z-10",children:"统计"})]})]}),(0,n.jsx)(eT.lG,{open:s.showImportDialog,onOpenChange:e=>l(t=>({...t,showImportDialog:e})),children:(0,n.jsxs)(eT.Cf,{className:"max-w-2xl h-[85vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl flex flex-col overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50/80 via-green-50/60 to-teal-50/80"}),(0,n.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-200/30 rounded-full blur-3xl"}),(0,n.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-200/30 to-emerald-200/30 rounded-full blur-2xl"}),(0,n.jsxs)("div",{className:"relative z-10 flex flex-col h-full min-h-0",children:[(0,n.jsxs)(eT.c7,{className:"space-y-4 pb-4 flex-shrink-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,n.jsx)("div",{className:"relative p-2 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-lg",children:(0,n.jsx)(eC,{className:"w-5 h-5 text-white"})})]}),(0,n.jsx)(eT.L3,{className:"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:"批量导入对话"})]}),(0,n.jsxs)(eT.rr,{className:"text-gray-600 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/50",children:["每行格式：",(0,n.jsx)("code",{className:"bg-emerald-100 text-emerald-800 px-2 py-1 rounded-lg font-mono text-sm",children:"声音名称@对话内容"}),"。支持的声音名称请参考声音库。"]})]}),(0,n.jsxs)("div",{className:"space-y-6 flex-1 overflow-y-auto min-h-0 pr-2 pb-2",children:[(0,n.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsx)("label",{className:"text-sm font-semibold text-gray-800",children:"对话内容"}),(0,n.jsxs)("button",{onClick:m,className:"relative overflow-hidden px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl transition-all duration-300 hover:scale-105 flex items-center gap-1.5 shadow-md hover:shadow-lg group",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,n.jsx)(eS,{className:"w-3 h-3 relative z-10"}),(0,n.jsx)("span",{className:"relative z-10",children:"加载示例"})]})]}),(0,n.jsx)("textarea",{value:s.importText,onChange:e=>u(e.target.value),placeholder:`示例格式：
Adam@你好！今天过得怎么样？
Jessica@我今天过得很好，谢谢你的关心！
Brian@那真是太好了，有什么特别的事情吗？`,className:"w-full h-32 p-4 border-2 border-emerald-200/60 rounded-xl resize-none focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-400 transition-all duration-300 text-sm bg-white/80 backdrop-blur-sm placeholder-gray-400"}),s.showSample&&(0,n.jsx)("div",{className:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50",children:(0,n.jsxs)("div",{className:"text-xs text-blue-700 flex items-center gap-2 font-medium",children:[(0,n.jsx)("div",{className:"p-1 bg-blue-500 rounded-full",children:(0,n.jsx)(eS,{className:"w-3 h-3 text-white"})}),"已加载示例文本，您可以直接导入或修改后导入"]})})]}),(0,n.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,n.jsx)("label",{className:"text-sm font-semibold text-gray-800 mb-3 block",children:"导入模式"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,n.jsxs)("label",{className:"relative cursor-pointer group",children:[(0,n.jsx)("input",{type:"radio",checked:"replace"===s.importMode,onChange:()=>l(e=>({...e,importMode:"replace"})),className:"sr-only"}),(0,n.jsx)("div",{className:`p-3 rounded-xl border-2 transition-all duration-300 ${"replace"===s.importMode?"border-orange-400 bg-gradient-to-r from-orange-50 to-red-50 shadow-lg":"border-gray-200 bg-white/80 hover:border-orange-300 hover:bg-orange-50/50"}`,children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:`w-4 h-4 rounded-full border-2 flex items-center justify-center ${"replace"===s.importMode?"border-orange-500 bg-orange-500":"border-gray-300"}`,children:"replace"===s.importMode&&(0,n.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,n.jsx)("span",{className:`text-sm font-medium ${"replace"===s.importMode?"text-orange-700":"text-gray-700"}`,children:"替换现有对话"})]})})]}),(0,n.jsxs)("label",{className:"relative cursor-pointer group",children:[(0,n.jsx)("input",{type:"radio",checked:"append"===s.importMode,onChange:()=>l(e=>({...e,importMode:"append"})),className:"sr-only"}),(0,n.jsx)("div",{className:`p-3 rounded-xl border-2 transition-all duration-300 ${"append"===s.importMode?"border-emerald-400 bg-gradient-to-r from-emerald-50 to-green-50 shadow-lg":"border-gray-200 bg-white/80 hover:border-emerald-300 hover:bg-emerald-50/50"}`,children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:`w-4 h-4 rounded-full border-2 flex items-center justify-center ${"append"===s.importMode?"border-emerald-500 bg-emerald-500":"border-gray-300"}`,children:"append"===s.importMode&&(0,n.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,n.jsx)("span",{className:`text-sm font-medium ${"append"===s.importMode?"text-emerald-700":"text-gray-700"}`,children:"追加到现有对话"})]})})]})]})]}),(s.importPreview.length>0||s.importErrors.length>0||s.importWarnings.length>0)&&(0,n.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,n.jsx)("div",{className:"p-1.5 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg",children:(0,n.jsx)(ej.A,{className:"w-4 h-4 text-white"})}),(0,n.jsx)("label",{className:"text-sm font-semibold text-gray-800",children:"预览结果"}),(0,n.jsxs)("div",{className:"ml-auto flex items-center gap-2 text-xs",children:[(0,n.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-700 rounded-lg font-medium",children:[s.importPreview.length,"行有效"]}),s.importErrors.length>0&&(0,n.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-700 rounded-lg font-medium",children:[s.importErrors.length,"行错误"]}),s.importWarnings.length>0&&(0,n.jsxs)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg font-medium",children:[s.importWarnings.length,"个警告"]})]})]}),(0,n.jsxs)("div",{className:"max-h-32 overflow-y-auto bg-white/80 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/30 space-y-2",children:[s.importPreview.map((e,r)=>{let o=t.find(t=>t.id===e.voice);return(0,n.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-green-50/80 rounded-lg border border-green-200/50",children:[(0,n.jsx)("div",{className:"p-1 bg-green-500 rounded-full",children:(0,n.jsx)(ej.A,{className:"w-3 h-3 text-white"})}),(0,n.jsxs)("span",{className:"text-sm flex-1",children:[(0,n.jsx)("span",{className:"font-semibold text-green-700 bg-green-100 px-2 py-0.5 rounded-lg",children:o?.name}),(0,n.jsx)("span",{className:"mx-2 text-gray-400",children:"→"}),(0,n.jsx)("span",{className:"text-gray-700",children:e.text.length>50?`${e.text.substring(0,50)}...`:e.text})]})]},r)}),s.importWarnings.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-yellow-50/80 rounded-lg border border-yellow-200/50",children:[(0,n.jsx)("div",{className:"p-1 bg-yellow-500 rounded-full",children:(0,n.jsx)(eh,{className:"w-3 h-3 text-white"})}),(0,n.jsx)("span",{className:"text-sm text-yellow-700 font-medium",children:e})]},t)),s.importErrors.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-red-50/80 rounded-lg border border-red-200/50",children:[(0,n.jsx)("div",{className:"p-1 bg-red-500 rounded-full",children:(0,n.jsx)(eh,{className:"w-3 h-3 text-white"})}),(0,n.jsx)("span",{className:"text-sm text-red-700 font-medium",children:e})]},t))]})]})]}),(0,n.jsxs)(eT.Es,{className:"flex gap-3 pt-4 pb-2 px-2 flex-shrink-0 border-t border-emerald-200/50 mt-4 bg-white/80 backdrop-blur-sm",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>l(e=>({...e,showImportDialog:!1,importText:"",importPreview:[],importErrors:[],importWarnings:[],showSample:!1})),className:"flex-1 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 rounded-xl py-2.5 font-medium",children:"取消"}),(0,n.jsx)(i.$,{onClick:p,disabled:0===s.importPreview.length||s.isImporting,className:"flex-1 min-w-[140px] bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl py-2.5 font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:s.isImporting?(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,n.jsx)("span",{children:"导入中..."})]}):(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)(eC,{className:"w-4 h-4"}),(0,n.jsxs)("span",{children:["确认导入(",s.importPreview.length,"行)"]})]})})]})]})]})}),(0,n.jsx)(eT.lG,{open:s.showStatsDialog,onOpenChange:e=>l(t=>({...t,showStatsDialog:e})),children:(0,n.jsxs)(eT.Cf,{className:"max-w-lg",children:[(0,n.jsx)(eT.c7,{children:(0,n.jsxs)(eT.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(eA,{className:"w-5 h-5 text-purple-600"}),"对话统计信息"]})}),(()=>{let r=function(e,t){let r=e.length,n=e.reduce((e,t)=>e+t.text.length,0),o=r>0?Math.round(n/r):0,s=new Map;e.forEach(e=>{let t=s.get(e.voice)||0;s.set(e.voice,t+1)});let a=Array.from(s.entries()).map(([e,n])=>({voice:t.find(t=>t.id===e)||{id:e,name:"未知声音",gender:"neutral",description:""},count:n,percentage:Math.round(n/r*100)})).sort((e,t)=>t.count-e.count);return{totalLines:r,totalCharacters:n,voiceDistribution:a,averageLength:o}}(e,t);return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.totalLines}),(0,n.jsx)("div",{className:"text-sm text-blue-700",children:"对话行数"})]}),(0,n.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:r.totalCharacters}),(0,n.jsx)("div",{className:"text-sm text-green-700",children:"总字符数"})]})]}),(0,n.jsxs)("div",{className:"bg-purple-50 p-3 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:r.averageLength}),(0,n.jsx)("div",{className:"text-sm text-purple-700",children:"平均每行字符数"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"声音使用分布"}),(0,n.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:r.voiceDistribution.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:`w-3 h-3 rounded-full ${"male"===e.voice.gender?"bg-blue-500":"female"===e.voice.gender?"bg-pink-500":"bg-gray-500"}`}),(0,n.jsx)("span",{className:"text-sm font-medium",children:e.voice.name})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[e.count,"次"]}),(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage,"%)"]})]})]},t))})]})]})})(),(0,n.jsx)(eT.Es,{children:(0,n.jsx)(i.$,{variant:"outline",onClick:()=>l(e=>({...e,showStatsDialog:!1})),children:"关闭"})})]})})]})},eI=e=>({getPosition:(0,a.useCallback)(()=>{let t=e.current;if(!t)return 0;let r=window.getSelection();if(r&&r.rangeCount>0){let e=r.getRangeAt(0),n=e.cloneRange();return n.selectNodeContents(t),n.setEnd(e.endContainer,e.endOffset),n.toString().length}return 0},[e]),setPosition:(0,a.useCallback)(t=>{let r=e.current;if(!r||t<0)return;let n=window.getSelection(),o=document.createRange(),s=0,a=!1;if(0===r.childNodes.length){r.focus();return}(function e(r){if(!a){if(r.nodeType===Node.TEXT_NODE){let e=s+r.length;t<=e?(o.setStart(r,t-s),o.setEnd(r,t-s),a=!0):s=e}else if(r.nodeType===Node.ELEMENT_NODE){for(let t of Array.from(r.childNodes))if(e(t),a)return}}})(r),a||(o.selectNodeContents(r),o.collapse(!1)),n&&(n.removeAllRanges(),n.addRange(o))},[e])}),eM=e=>e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\[([a-zA-Z\s]+)\]/g,'<span class="emotion-tag">[$1]</span>'):"",eR=({value:e,onChange:t,onFocus:r,onBlur:o,placeholder:s,className:i,maxLength:l=1e3})=>{let c=(0,a.useRef)(null),m=(0,a.useRef)(""),{getPosition:d,setPosition:u}=eI(c);(0,a.useEffect)(()=>{if(c.current){let t=eM(e);if(e!==m.current||c.current.innerHTML!==t){let e=d();c.current.innerHTML=t,u(e)}}m.current=e},[e,d,u]);let p=(0,a.useCallback)(()=>{if(c.current){let r=c.current.innerText;if(r.length>l){let t=d();c.current.innerHTML=eM(e),u(Math.min(e.length,t));return}t(r)}},[t,l,e,d,u]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{children:`
        .emotion-tag {
          color: #8B5CF6; /* a nice purple color */
          font-weight: 600;
          background-color: rgba(139, 92, 246, 0.05);
          padding: 2px 1px;
          border-radius: 4px;
        }
      `}),(0,n.jsx)("div",{ref:c,contentEditable:!0,onInput:p,onFocus:r,onBlur:o,className:i,"data-placeholder":s,suppressContentEditableWarning:!0,style:{whiteSpace:"pre-wrap",wordWrap:"break-word"}})]})},eD=({line:e,index:t,voices:r,voiceIconMapping:o,voiceIcons:s,isActive:a,onSelectLine:l,onUpdateText:c,onRemoveLine:m,canRemove:d,onTextInputFocus:u,onEditVoice:p})=>{let b=r.find(t=>t.id===e.voice);return(0,n.jsxs)("div",{"data-dialogue-line-id":e.id,className:`group relative flex items-center gap-3 p-3 pl-8 rounded-xl border transition-all duration-300 ${a?"border-purple-400 bg-gradient-to-r from-purple-50/50 to-blue-50/50 shadow-md":"border-gray-200/80 hover:border-gray-300 hover:bg-gray-50/30"}`,children:[(0,n.jsxs)("button",{onClick:()=>p?.(e.id),className:`relative w-7 h-7 rounded-full overflow-hidden shadow-md transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-400 flex-shrink-0 ${a?"ring-2 ring-purple-300 scale-105":"hover:ring-2 hover:ring-purple-200"}`,title:`点击为 ${b?.name||`说话者 ${t+1}`} 选择声音`,children:[(0,n.jsx)("img",{src:o[e.voice]||s[0],alt:b?.name,className:"w-full h-full object-cover",onError:e=>{let t=e.target;t.style.display="none";let r=t.parentElement;r&&b&&(r.innerHTML=`
                <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold text-sm ${"male"===b.gender?"bg-gradient-to-br from-blue-500 to-blue-700":"female"===b.gender?"bg-gradient-to-br from-pink-500 to-pink-700":"bg-gradient-to-br from-gray-500 to-gray-700"}">
                  ${b.name.charAt(0).toUpperCase()}
                </div>
              `)}}),a&&(0,n.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full border border-white",children:(0,n.jsx)("div",{className:"absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75"})})]}),(0,n.jsxs)("div",{className:"flex-shrink-0 min-w-0 w-20",children:[(0,n.jsx)("div",{className:`font-medium text-sm truncate transition-colors duration-300 ${a?"text-purple-700":"text-gray-700"}`,children:b?.name||"未选择"}),b&&(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"male"===b.gender?"♂ 男声":"female"===b.gender?"♀ 女声":"⚲ 中性"})]}),(0,n.jsx)("div",{className:"flex-1 min-w-0",children:(0,n.jsx)(eR,{value:e.text,onChange:t=>c(e.id,t),onFocus:()=>u?.(e.id),placeholder:`${b?.name||`说话者 ${t+1}`} 说...`,className:`w-full bg-white/80 text-base py-2.5 px-3 border rounded-md transition-all duration-300 min-h-[42px] outline-none ${a?"border-purple-300 bg-white focus:border-purple-400":"border-gray-200 focus:border-blue-400 focus:bg-white"}`,maxLength:1e3})}),(0,n.jsx)("div",{className:"flex-shrink-0 text-xs text-gray-400 w-12 text-right",children:e.text.length}),d&&(0,n.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>m(e.id),className:"flex-shrink-0 w-8 h-8 text-gray-400 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-all duration-300",title:"删除此对话",children:(0,n.jsx)(ep.A,{className:"w-4 h-4"})}),(0,n.jsx)("div",{className:`absolute -top--1 -left-0 w-6 h-6 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg border-2 border-white transition-all duration-300 ${a?"bg-gradient-to-r from-purple-500 to-pink-500 scale-110 shadow-xl":"bg-gradient-to-r from-blue-500 to-purple-500 group-hover:scale-105"}`,children:t+1})]})},eP=({dialogueLines:e,voices:t,voiceIconMapping:r,voiceIcons:o,activeDialogueLineId:s,onSelectLine:i,onUpdateText:l,onRemoveLine:c,onTextInputFocus:m,onEditVoice:d,itemHeight:u=80,containerHeight:p=350,overscan:b=2,className:g=""})=>{let[f,x]=(0,a.useState)(0),h=(0,a.useRef)(null),y=(0,a.useRef)(!1),w=(0,a.useRef)(),v=Math.ceil(p/u),k=Math.max(0,Math.floor(f/u)-b),j=Math.min(e.length,k+v+2*b),N=e.slice(k,j),z=e.length*u,C=(0,a.useCallback)(e=>{x(e.currentTarget.scrollTop),y.current=!0,w.current&&clearTimeout(w.current),w.current=setTimeout(()=>{y.current=!1},150)},[]),A=(0,a.useCallback)(t=>{let r=e.findIndex(e=>e.id===t);if(-1!==r&&h.current){let e=r*u;h.current.scrollTo({top:e,behavior:"smooth"})}},[e,u]);return(0,a.useEffect)(()=>{if(null!==s&&!y.current){let t=e.findIndex(e=>e.id===s);if(-1!==t){let e=t*u,r=e+u,n=f+p;(e<f||r>n)&&A(s)}}},[s,e,u,p,f,A]),(0,a.useEffect)(()=>()=>{w.current&&clearTimeout(w.current)},[]),(0,n.jsx)("div",{ref:h,className:`overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ${g}`,style:{height:p},onScroll:C,children:(0,n.jsx)("div",{style:{height:z,position:"relative"},children:(0,n.jsx)("div",{style:{transform:`translateY(${k*u}px)`,position:"absolute",top:0,left:0,right:0},children:N.map((a,p)=>(0,n.jsx)("div",{style:{height:u,paddingBottom:"12px"},children:(0,n.jsx)(eD,{line:a,index:k+p,voices:t,voiceIconMapping:r,voiceIcons:o,isActive:s===a.id,onSelectLine:i,onUpdateText:l,onRemoveLine:c,canRemove:e.length>1,onTextInputFocus:m,onEditVoice:d})},a.id))})})})},e_=({dialogueLines:e,voices:t,voiceIconMapping:r,voiceIcons:o,activeDialogueLineId:s,onSelectLine:a,onUpdateText:i,onRemoveLine:l,onTextInputFocus:c,onEditVoice:m,containerHeight:d=350,className:u=""})=>(0,n.jsx)("div",{className:`space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pr-2 ${u}`,style:{maxHeight:d},children:e.map((d,u)=>(0,n.jsx)(eD,{line:d,index:u,voices:t,voiceIconMapping:r,voiceIcons:o,isActive:s===d.id,onSelectLine:a,onUpdateText:i,onRemoveLine:l,canRemove:e.length>1,onTextInputFocus:c,onEditVoice:m},d.id))}),eU=({dialogueLines:e,virtualThreshold:t=20,...r})=>e.length>t?(0,n.jsx)(eP,{dialogueLines:e,...r}):(0,n.jsx)(e_,{dialogueLines:e,...r});var eF=r(4426),eO=r(9556),eB=r(1702),eH=r(5478);function eK({className:e=""}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:`jsx-e0e68512b4dc21ba absolute inset-0 aurora-layer-1 ${e}`}),(0,n.jsx)("div",{className:`jsx-e0e68512b4dc21ba absolute inset-0 aurora-layer-2 ${e}`}),(0,n.jsx)("div",{className:`jsx-e0e68512b4dc21ba absolute inset-0 aurora-layer-3 ${e}`}),(0,n.jsx)("div",{className:`jsx-e0e68512b4dc21ba absolute inset-0 aurora-layer-4 ${e}`}),(0,n.jsx)(s(),{id:"e0e68512b4dc21ba",children:".aurora-layer-1.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:-moz-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:-o-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));-webkit-background-size:400%400%;-moz-background-size:400%400%;-o-background-size:400%400%;background-size:400%400%;-webkit-animation:aurora-flow-1 8s ease-in-out infinite;-moz-animation:aurora-flow-1 8s ease-in-out infinite;-o-animation:aurora-flow-1 8s ease-in-out infinite;animation:aurora-flow-1 8s ease-in-out infinite;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-2.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:-moz-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:-o-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:linear-gradient(-45deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));-webkit-background-size:350%350%;-moz-background-size:350%350%;-o-background-size:350%350%;background-size:350%350%;animation:aurora-flow-2 12s ease-in-out infinite reverse;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-3.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:-moz-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:-o-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:linear-gradient(90deg,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));-webkit-background-size:300%300%;-moz-background-size:300%300%;-o-background-size:300%300%;background-size:300%300%;-webkit-animation:aurora-flow-3 10s ease-in-out infinite;-moz-animation:aurora-flow-3 10s ease-in-out infinite;-o-animation:aurora-flow-3 10s ease-in-out infinite;animation:aurora-flow-3 10s ease-in-out infinite;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-4.jsx-e0e68512b4dc21ba{background:-webkit-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:-moz-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:-o-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:radial-gradient(ellipse at center,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:aurora-pulse 6s ease-in-out infinite alternate;-moz-animation:aurora-pulse 6s ease-in-out infinite alternate;-o-animation:aurora-pulse 6s ease-in-out infinite alternate;animation:aurora-pulse 6s ease-in-out infinite alternate;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}@-webkit-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-webkit-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-webkit-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-webkit-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-webkit-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-moz-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-moz-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-moz-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-moz-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-moz-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-o-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-o-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-o-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@keyframes aurora-flow-1{0%,100%{background-position:0%50%;-webkit-transform:translatex(0)scale(1);-moz-transform:translatex(0)scale(1);-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-webkit-transform:translatex(2px)scale(1.02);-moz-transform:translatex(2px)scale(1.02);-o-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-webkit-transform:translatex(0)scale(1);-moz-transform:translatex(0)scale(1);-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-webkit-transform:translatex(-2px)scale(.98);-moz-transform:translatex(-2px)scale(.98);-o-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-webkit-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-moz-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-o-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-webkit-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-webkit-transform:rotate(1deg);transform:rotate(1deg)}}@-moz-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-moz-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-moz-transform:rotate(1deg);transform:rotate(1deg)}}@-o-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-o-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-o-transform:rotate(1deg);transform:rotate(1deg)}}@keyframes aurora-flow-3{0%,100%{background-position:0%0%;-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-webkit-transform:rotate(1deg);-moz-transform:rotate(1deg);-o-transform:rotate(1deg);transform:rotate(1deg)}}@-webkit-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);transform:scale(1)}}@-moz-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-moz-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-moz-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-moz-transform:scale(1);transform:scale(1)}}@-o-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-o-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-o-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-o-transform:scale(1);transform:scale(1)}}@keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}}"})]})}let eW=({filteredVoices:e,onSelectVoice:t,currentVoiceId:r,previewingVoice:o,handleVoicePreview:s,voiceIconMapping:a,voiceIcons:i,listHeightClass:l="max-h-80",showSelectionIndicator:c=!0,favoriteVoiceIds:m=[],onToggleFavorite:d,showFavoriteButton:u=!1})=>(0,n.jsx)("div",{className:`${l} overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100`,children:e.length>0?e.map(e=>(0,n.jsxs)("div",{"data-voice-id":e.id,className:`relative p-2.5 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50/50 group/item ${r===e.id?"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100":""}`,onClick:()=>t(e.id),children:[(0,n.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,n.jsx)("div",{className:`relative w-10 h-10 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12 ${r===e.id?"ring-3 ring-blue-300 scale-110":""}`,children:a[e.id]?(0,n.jsx)("img",{src:a[e.id],alt:e.name,className:"w-full h-full object-cover",onError:t=>{let r=t.target;r.style.display="none";let n=r.parentElement;n&&(n.innerHTML=`
                          <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold ${"male"===e.gender?"bg-gradient-to-br from-blue-500 to-blue-700":"female"===e.gender?"bg-gradient-to-br from-pink-500 to-pink-700":"bg-gradient-to-br from-gray-500 to-gray-700"}">
                            ${e.name.charAt(0).toUpperCase()}
                          </div>
                        `)}}):(0,n.jsx)("div",{className:`w-full h-full rounded-full flex items-center justify-center text-white font-bold animate-pulse ${"male"===e.gender?"bg-gradient-to-br from-blue-400 to-blue-600":"female"===e.gender?"bg-gradient-to-br from-pink-400 to-pink-600":"bg-gradient-to-br from-gray-400 to-gray-600"}`,children:e.name.charAt(0).toUpperCase()})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"font-semibold text-gray-900 text-base truncate group-hover/item:text-blue-700 transition-colors duration-300",children:e.name}),(0,n.jsxs)("div",{className:`flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full flex-shrink-0 ${"male"===e.gender?"bg-blue-100 text-blue-700 border border-blue-200":"female"===e.gender?"bg-pink-100 text-pink-700 border border-pink-200":"bg-gray-100 text-gray-700 border border-gray-200"}`,children:[(0,n.jsx)("div",{className:`w-2 h-2 rounded-full ${"male"===e.gender?"bg-blue-500":"female"===e.gender?"bg-pink-500":"bg-gray-500"}`}),(0,n.jsx)("span",{children:"male"===e.gender?"男生":"female"===e.gender?"女生":"中性"})]}),r===e.id&&(0,n.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-ping flex-shrink-0 ml-10"})]}),(0,n.jsx)("div",{className:`text-sm leading-relaxed truncate transition-colors duration-300 ${"male"===e.gender?"text-blue-600 group-hover/item:text-blue-700":"female"===e.gender?"text-pink-600 group-hover/item:text-pink-700":"text-gray-600 group-hover/item:text-gray-700"}`,children:e.description})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[u&&d&&(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),d(e.id)},className:`button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 group/favorite shadow-lg ${m.includes(e.id)?"bg-gradient-to-r from-pink-100 to-red-100 hover:from-pink-200 hover:to-red-200":"bg-gradient-to-r from-gray-100 to-gray-200 hover:from-pink-100 hover:to-pink-200"}`,children:(0,n.jsx)(eg,{className:`w-4 h-4 transition-colors duration-300 ${m.includes(e.id)?"text-pink-600 fill-current":"text-gray-600 group-hover/favorite:text-pink-600"}`})}),(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),s(e.preview||null,e.id)},disabled:!e.preview,className:`button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 hover:rotate-12 group/play shadow-lg disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 ${o===e.id?"bg-gradient-to-r from-green-100 to-green-200 hover:from-green-200 hover:to-green-300":"bg-gradient-to-r from-gray-100 to-gray-200 hover:from-blue-100 hover:to-blue-200"}`,children:o===e.id?(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"})}):(0,n.jsx)(em,{className:"w-4 h-4 text-gray-600 group-hover/play:text-blue-600 ml-0.5 transition-colors duration-300"})})]})]}),c&&r===e.id&&(0,n.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-blue-400 via-purple-500 to-pink-400 rounded-r animate-pulse"})]},e.id)):(0,n.jsx)("div",{className:"p-8 text-center text-gray-500",children:(0,n.jsx)("p",{children:"没有找到匹配的声音。"})})}),eG=["https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"];function eq(e,t,r=!0){return{type:e,message:t,retryable:r}}let eV=(0,X.A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),eZ=(0,X.A)("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]),eJ=(0,X.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),eY=(0,X.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),eX=({error:e,onRetry:t,className:r=""})=>(0,n.jsx)("div",{className:`flex items-center justify-center min-h-screen ${r}`,children:(0,n.jsx)(l.Zp,{className:"w-full max-w-md mx-4",children:(0,n.jsxs)(l.Wu,{className:"flex flex-col items-center justify-center p-8",children:[(0,n.jsx)("div",{className:"mb-6",children:(()=>{switch(e.type){case"network":return(0,n.jsx)(eV,{className:"w-12 h-12 text-red-500"});case"parse":return(0,n.jsx)(eZ,{className:"w-12 h-12 text-orange-500"});case"empty":return(0,n.jsx)(eh,{className:"w-12 h-12 text-yellow-500"});default:return(0,n.jsx)(eJ,{className:"w-12 h-12 text-gray-500"})}})()}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 text-center",children:(()=>{switch(e.type){case"network":return"网络连接失败";case"parse":return"数据格式错误";case"empty":return"暂无声音数据";default:return"加载失败"}})()}),(0,n.jsx)("p",{className:"text-sm text-gray-600 text-center mb-6",children:(()=>{switch(e.type){case"network":return"无法连接到服务器，请检查网络连接后重试";case"parse":return"服务器返回的数据格式不正确，请联系技术支持";case"empty":return"当前没有可用的声音选项，请稍后再试或联系管理员";default:return"发生了未知错误，请尝试刷新页面或联系技术支持"}})()}),!1,(0,n.jsxs)("div",{className:"flex flex-col gap-3 w-full",children:[e.retryable&&(0,n.jsxs)(i.$,{onClick:t,className:"w-full flex items-center gap-2",variant:"default",children:[(0,n.jsx)(eY,{className:"w-4 h-4"}),"重试加载"]}),(0,n.jsx)(i.$,{onClick:()=>window.location.reload(),variant:"outline",className:"w-full",children:"刷新页面"})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500 text-center mt-4",children:"如果问题持续存在，请联系技术支持"})]})})}),eQ=(0,X.A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var e0=r(3964);let e5="tts_task_center_tasks",e1=(0,a.forwardRef)(({className:e="",getActualTaskIds:t},r)=>{let[o,s]=(0,a.useState)(!1),[l,c]=(0,a.useState)([]),[m,d]=(0,a.useState)(""),[u,p]=(0,a.useState)([]),[b,g]=(0,a.useState)(null),f=(0,a.useCallback)(e=>{try{localStorage.setItem(e5,JSON.stringify(e))}catch(e){console.error("Failed to save tasks to localStorage:",e)}},[]);(0,a.useEffect)(()=>{try{let e=localStorage.getItem(e5);if(e){let t=JSON.parse(e);c(t)}}catch(e){console.error("Failed to load tasks from localStorage:",e)}},[]),(0,a.useEffect)(()=>{if(m.trim()){let e=m.toLowerCase().trim();p(l.filter(t=>t.taskId.toLowerCase().includes(e)))}else p(l)},[l,m]);let x=(0,a.useCallback)(e=>{let t={taskId:e,createdAt:Date.now(),status:"processing"};c(e=>{let r=[t,...e];return f(r),r})},[f]),h=(0,a.useCallback)((e,t,r)=>{c(n=>{let o=n.map(n=>n.taskId===e?{...n,status:t,downloadUrl:r,isRefreshing:!1}:n);return f(o),o})},[f]);(0,a.useImperativeHandle)(r,()=>({addTask:x,updateTaskStatus:h}),[x,h]);let y=(0,a.useCallback)(async e=>{try{c(t=>t.map(t=>t.taskId===e?{...t,isRefreshing:!0}:t));let r=eO.tC.getAccessToken();if(!r)throw Error("未找到访问令牌，请重新登录");let n=t?t(e):[e];console.log("[TASK-CENTER] Refresh request:",{displayTaskId:e,actualTaskIds:n});let o=null,s=null;for(let e of n)try{console.log(`[TASK-CENTER] Trying to fetch status for physical task: ${e}`);let t=await fetch(`https://myaitts-worker.panxuchao19951206.workers.dev/api/tts/status/${e}`,{method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!t.ok){let r=await t.json().catch(()=>({}));o=Error(r.error||`HTTP ${t.status}: ${t.statusText}`),console.log(`[TASK-CENTER] Physical task ${e} failed:`,o.message);continue}let n=await t.json();if("complete"===n.status||"completed"===n.status){s=n,console.log(`[TASK-CENTER] Found successful task: ${e}`,n);break}if("processing"===n.status){s=n,console.log(`[TASK-CENTER] Found processing task: ${e}`,n);break}o=Error(`Task ${e} status: ${n.status}`),console.log(`[TASK-CENTER] Physical task ${e} not successful:`,n.status);continue}catch(t){o=t,console.log(`[TASK-CENTER] Error fetching physical task ${e}:`,t.message);continue}if(!s)throw o||Error("所有物理任务ID查询都失败了");let a=s;if("complete"===a.status||"completed"===a.status){let t=a.audioUrl||a.downloadUrl||`https://r2-assets.aispeak.top/audios/${e}.mp3`;h(e,"complete",t),console.log(`[TASK-CENTER] Updated display task ${e} to complete`)}else"processing"===a.status||"pending"===a.status||"running"===a.status?(h(e,"processing"),console.log(`[TASK-CENTER] Updated display task ${e} to processing`)):"failed"===a.status||"error"===a.status?(h(e,"failed"),console.log(`[TASK-CENTER] Updated display task ${e} to failed`)):(h(e,"failed"),console.log(`[TASK-CENTER] Updated display task ${e} to failed (unknown status: ${a.status})`))}catch(t){console.error("[TASK-CENTER] Failed to fetch task status:",t),c(t=>t.map(t=>t.taskId===e?{...t,isRefreshing:!1}:t))}},[h,t]),w=(0,a.useCallback)(async e=>{try{await navigator.clipboard.writeText(e),g(e),setTimeout(()=>g(null),2e3)}catch(e){console.error("Failed to copy task ID:",e)}},[]),v=(0,a.useCallback)(async(e,t)=>{try{console.log("[TASK-CENTER] Initiating secure download:",{downloadUrl:e,taskId:t});let r=new Date,n=r.getFullYear(),o=String(r.getMonth()+1).padStart(2,"0"),s=String(r.getDate()).padStart(2,"0"),a=String(r.getHours()).padStart(2,"0"),i=String(r.getMinutes()).padStart(2,"0"),l=String(r.getSeconds()).padStart(2,"0"),c=`tts_${n}${o}${s}_${a}${i}${l}.mp3`;if(e){let t=eO.tC.getAccessToken();if(!t)throw console.error("[TASK-CENTER] No token available for download"),Error("认证失败，请重新登录");let r=await fetch(e,{method:"GET",headers:{Authorization:`Bearer ${t}`,Accept:"application/octet-stream"}});if(!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let n=await r.blob(),o=URL.createObjectURL(n),s=document.createElement("a");s.href=o,s.download=c,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o),console.log("[TASK-CENTER] Secure download completed successfully")}else throw console.error("[TASK-CENTER] No download URL provided"),Error("Download URL not available")}catch(e){console.error("[TASK-CENTER] Download failed:",e)}},[]),k=(0,a.useCallback)(()=>{c([]),f([])},[f]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("button",{onClick:()=>s(!0),className:`relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm ${e}`,children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,n.jsx)(eQ,{className:"w-4 h-4 relative z-10"}),(0,n.jsx)("span",{className:"relative z-10",children:"任务中心"}),l.length>0&&(0,n.jsx)("span",{className:"relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center",children:l.length>99?"99+":l.length})]}),(0,n.jsx)(eT.lG,{open:o,onOpenChange:s,children:(0,n.jsxs)(eT.Cf,{className:"max-w-4xl max-h-[80vh] flex flex-col",children:[(0,n.jsx)(eT.c7,{children:(0,n.jsxs)(eT.L3,{className:"flex items-center gap-2 text-xl font-bold",children:[(0,n.jsx)(eQ,{className:"w-5 h-5 text-purple-600"}),"任务中心",(0,n.jsxs)("span",{className:"text-sm font-normal text-gray-500",children:["(",l.length," 个任务)"]})]})}),(0,n.jsxs)("div",{className:"flex items-center gap-3 py-4 border-b border-gray-100",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(eu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,n.jsx)(Y.p,{placeholder:"搜索任务ID...",value:m,onChange:e=>d(e.target.value),className:"pl-10 h-10 border-2 border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-50"})]}),(0,n.jsxs)(i.$,{onClick:k,variant:"outline",size:"sm",className:"h-10 px-4 border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300",children:[(0,n.jsx)(ep.A,{className:"w-4 h-4 mr-1"}),"清空"]})]}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===u.length?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 text-gray-500",children:[(0,n.jsx)(eQ,{className:"w-12 h-12 mb-4 text-gray-300"}),(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"暂无任务"}),(0,n.jsx)("p",{className:"text-sm text-center max-w-sm",children:m?"没有找到匹配的任务":"您还没有创建任何任务，开始使用AI语音转换功能来创建您的第一个任务吧！"})]}):(0,n.jsx)("div",{className:"space-y-3 p-1",children:u.map((e,t)=>(0,n.jsx)(e7,{task:e,index:t,onCopy:w,onDownload:v,onRefresh:y,copiedTaskId:b},e.taskId))})})]})})]})});e1.displayName="TaskCenter";let e2=e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/1e3);if(r<60)return"刚刚";if(r<3600){let e=Math.floor(r/60);return`${e}分钟前`}if(r<86400){let e=Math.floor(r/3600);return`${e}小时前`}{let e=Math.floor(r/86400);return`${e}天前`}},e3=e=>{switch(e){case"processing":return"bg-orange-100 text-orange-600";case"complete":return"bg-green-100 text-green-600";case"failed":return"bg-red-100 text-red-600";default:return"bg-gray-100 text-gray-600"}},e6=e=>{switch(e){case"processing":return"处理中";case"complete":return"已完成";case"failed":return"失败";default:return"未知"}},e7=({task:e,index:t,onCopy:r,onDownload:o,onRefresh:s,copiedTaskId:a})=>(0,n.jsx)("div",{className:"bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200",children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsxs)("span",{className:"text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg",children:["#",t+1]}),(0,n.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,n.jsx)(ey.A,{className:"w-3 h-3"}),e2(e.createdAt)]}),e.status&&(0,n.jsx)("span",{className:`text-xs px-2 py-1 rounded-lg font-medium ${e3(e.status)}`,children:e6(e.status)})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,n.jsx)("span",{className:"text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded border truncate max-w-[700px]",children:e.taskId}),(0,n.jsx)("button",{onClick:()=>r(e.taskId),className:"p-1 rounded hover:bg-gray-100 transition-colors duration-200",title:"复制任务ID",children:a===e.taskId?(0,n.jsx)(e0.A,{className:"w-3 h-3 text-green-600"}):(0,n.jsx)(ez,{className:"w-3 h-3 text-gray-400"})})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:["complete"===e.status&&e.downloadUrl&&(0,n.jsx)("button",{onClick:()=>o(e.downloadUrl,e.taskId),className:"p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200",title:"下载音频",children:(0,n.jsx)(ed,{className:"w-4 h-4"})}),(0,n.jsx)("button",{onClick:()=>s(e.taskId),disabled:e.isRefreshing,className:`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${e.isRefreshing?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer"}`,title:"刷新任务状态",children:(0,n.jsx)(eY,{className:`w-4 h-4 ${e.isRefreshing?"animate-spin":""}`})})]})]})}),e9=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("span",{className:"text-white-600 font-bold text-lg",children:"♂"})}),e4=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("span",{className:"text-white-600 font-bold text-lg",children:"♀"})}),e8=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("span",{className:"text-gray-600 font-bold text-lg",children:"⚲"})}),te=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://img.icons8.com/3d-fluency/94/person-male--v3.png",alt:"Male",className:"w-4 h-4 object-cover"})}),tt=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://img.icons8.com/3d-fluency/94/person-female--v3.png",alt:"Female",className:"w-4 h-4 object-cover"})}),tr=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/us.svg",alt:"US Flag",className:"w-4 h-3 object-cover rounded-sm"})}),tn=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/jp.svg",alt:"JP Flag",className:"w-4 h-3 object-cover rounded-sm"})}),to=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/es.svg",alt:"ES Flag",className:"w-4 h-3 object-cover rounded-sm"})}),ts=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/kr.svg",alt:"KR Flag",className:"w-4 h-3 object-cover rounded-sm"})}),ta=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/fr.svg",alt:"FR Flag",className:"w-4 h-3 object-cover rounded-sm"})}),ti=({className:e})=>(0,n.jsx)("div",{className:`flex items-center justify-center ${e}`,children:(0,n.jsx)("img",{src:"https://img.icons8.com/pulsar-gradient/48/infinity.png",alt:"Infinity",className:"w-4 h-4 object-contain"})}),tl=({value:e,onChange:t,options:r,className:o="",hoverColor:s="green"})=>{let[i,l]=(0,a.useState)(!1),c=(0,a.useRef)(null),m=r.find(t=>t.value===e);(0,a.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let d={green:"group-hover:text-green-500 focus:border-green-400 focus:ring-green-500/10 hover:border-green-300",orange:"group-hover:text-orange-500 focus:border-orange-400 focus:ring-orange-500/10 hover:border-orange-300"};return(0,n.jsxs)("div",{className:"relative group",ref:c,children:[(0,n.jsx)("div",{className:`absolute inset-0 bg-gradient-to-r ${{green:"from-green-500/5 to-blue-500/5",orange:"from-orange-500/5 to-red-500/5"}[s]} rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300`}),(0,n.jsxs)("button",{onClick:()=>l(!i),className:`relative w-full px-4 py-3 text-sm border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl hover:border-gray-300 ${d[s]} transition-all duration-300 outline-none cursor-pointer shadow-sm hover:shadow-lg font-medium text-gray-700 flex items-center justify-between`,children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("span",{className:`text-gray-400 ${d[s]} transition-colors duration-300`,children:m?.icon}),(0,n.jsx)("span",{children:m?.label})]}),(0,n.jsx)(Q,{className:`w-4 h-4 text-gray-400 ${d[s]} transition-all duration-300 ${i?"rotate-180":""}`})]}),i&&(0,n.jsx)("div",{className:"absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden",children:r.map(r=>(0,n.jsxs)("button",{onClick:()=>{t(r.value),l(!1)},className:`w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-all duration-200 flex items-center gap-3 ${e===r.value?"bg-blue-50 text-blue-700":"text-gray-700"}`,children:[(0,n.jsx)("span",{className:`${e===r.value?"text-blue-600":"text-gray-400"} transition-colors duration-200`,children:r.icon}),(0,n.jsx)("span",{children:r.label})]},r.value))})]})};function tc(){let{voices:e,isLoading:t,error:r,voiceIconMapping:o,retry:c}=function(){let[e,t]=(0,a.useState)({voices:[],isLoading:!0,error:null,voiceIconMapping:{}}),[r,n]=(0,a.useState)(!1),o=(0,a.useCallback)(()=>"https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json",[]),s=(0,a.useCallback)(e=>{if(!r)return{};let t=function(e){let t=[...e];for(let e=t.length-1;e>0;e--){let r=Math.floor(Math.random()*(e+1));[t[e],t[r]]=[t[r],t[e]]}return t}(eG),n={};return e.forEach((e,r)=>{n[e.id]=t[r%t.length]}),n},[r]),i=(0,a.useCallback)(async()=>{try{t(e=>({...e,isLoading:!0,error:null}));let e=o(),r=await fetch(e,{cache:"no-cache"});if(!r.ok)throw eq("network",`无法获取声音列表: ${r.status} ${r.statusText}`,!0);let n=await r.json();if(!Array.isArray(n))throw eq("parse","声音数据格式错误：期望数组格式",!0);if(0===n.length)throw eq("empty","声音列表为空，请联系管理员",!0);let a=n.filter(e=>!e.id||!e.name||!e.gender||!e.description);a.length>0&&console.warn("发现无效的声音数据:",a);let i=s(n);t({voices:n,isLoading:!1,error:null,voiceIconMapping:i})}catch(r){let e;console.error("获取声音数据失败:",r),e=r instanceof TypeError&&r.message.includes("fetch")?eq("network","网络连接失败，请检查网络后重试",!0):r.type?r:eq("unknown",r.message||"加载声音配置时发生未知错误",!0),t(t=>({...t,isLoading:!1,error:e}))}},[o,s]),l=(0,a.useCallback)(()=>{e.error?.retryable&&i()},[e.error?.retryable,i]);return{...e,retry:l,refetch:i}}(),m=["https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"],[d,u]=(0,a.useState)(""),[p,b]=(0,a.useState)(""),[g,f]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),[y,w]=(0,a.useState)(!1),[v,k]=(0,a.useState)([.58]),[j,N]=(0,a.useState)([.75]),[z,C]=(0,a.useState)([.3]),[A,S]=(0,a.useState)([1]),[T,$]=(0,a.useState)(!1),[E,L]=(0,a.useState)(!1),[I,M]=(0,a.useState)("eleven_multilingual_v2"),[R,D]=(0,a.useState)(!1),[P,_]=(0,a.useState)(!1),[U,F]=(0,a.useState)(""),[O,B]=(0,a.useState)(!1),[H,K]=(0,a.useState)(null),[W,G]=(0,a.useState)({streamUrl:null,downloadUrl:null,secureStreamUrl:null}),[V,Z]=(0,a.useState)("00:00"),[X,ez]=(0,a.useState)("00:00"),[eC,eA]=(0,a.useState)(0),[eS,e$]=(0,a.useState)(0),[eE,eI]=(0,a.useState)(!1),[eM,eR]=(0,a.useState)(null),[eD,eP]=(0,a.useState)(!1),[e_,eV]=(0,a.useState)(!1),[eZ,eJ]=(0,a.useState)(null),[eY,eQ]=(0,a.useState)(null),[e0,e5]=(0,a.useState)(!1),{toast:e2}=(0,eB.dj)(),e3=async e=>{try{let t=eO.tC.getAccessToken();if(!t)return console.error("[SECURE-AUDIO] No token available for audio loading"),null;console.log("[SECURE-AUDIO] Loading audio with Authorization header:",e);let r=await fetch(e,{method:"GET",headers:{Authorization:`Bearer ${t}`,Accept:"audio/mpeg"}});if(!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let n=await r.blob(),o=URL.createObjectURL(n);return console.log("[SECURE-AUDIO] Audio loaded successfully, blob URL created"),o}catch(e){return console.error("[SECURE-AUDIO] Failed to load audio:",e),null}},e6=async(e,t)=>{try{let r=eO.tC.getAccessToken();if(!r)return console.error("[SECURE-DOWNLOAD] No token available for download"),eR("认证失败，请重新登录"),!1;console.log("[SECURE-DOWNLOAD] Downloading audio with Authorization header:",e);let n=await fetch(e,{method:"GET",headers:{Authorization:`Bearer ${r}`,Accept:"application/octet-stream"}});if(!n.ok)throw Error(`HTTP ${n.status}: ${n.statusText}`);let o=await n.blob(),s=URL.createObjectURL(o),a=document.createElement("a");return a.href=s,a.download=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),console.log("[SECURE-DOWNLOAD] Download completed successfully"),!0}catch(e){return console.error("[SECURE-DOWNLOAD] Download failed:",e),eR("下载失败，请检查网络连接或重试"),!1}},[e7,tc]=(0,a.useState)(!1),[tm,td]=(0,a.useState)(!1),[tu,tp]=(0,a.useState)(""),[tb,tg]=(0,a.useState)({isVip:!1,expireAt:0}),[tf,tx]=(0,a.useState)(""),[th,ty]=(0,a.useState)(""),[tw,tv]=(0,a.useState)("all"),[tk,tj]=(0,a.useState)("all"),[tN,tz]=(0,a.useState)(!1),[tC,tA]=(0,a.useState)([]),[tS,tT]=(0,a.useState)(!1),[t$,tE]=(0,a.useState)("all"),[tL,tI]=(0,a.useState)("all"),[tM,tR]=(0,a.useState)(""),[tD,tP]=(0,a.useState)(""),[t_,tU]=(0,a.useState)(!1),[tF,tO]=(0,a.useState)(null),[tB,tH]=(0,a.useState)("idle"),[tK,tW]=(0,a.useState)(""),[tG,tq]=(0,a.useState)(null),[tV,tZ]=(0,a.useState)([]),tJ=(0,a.useRef)(null),tY="tts_task_mapping",[tX,tQ]=(0,a.useState)(0),[t0,t5]=(0,a.useState)([]),[t1,t2]=(0,a.useState)(null),t3=(0,a.useRef)(null),t6="tts_retry_task_data",[t7,t9]=(0,a.useState)({frontendAttempts:0,frontendMaxAttempts:1,frontendInProgress:!1,frontendStartTime:0,datacenterAttempts:0,datacenterMaxAttempts:1,backupApiAttempts:0,backupApiMaxAttempts:2,usingBackupApi:!1,backupApiStartTime:0,currentBackupIndex:-1,maxBackupIndex:-1,isLocked:!1,lockReason:"",activeRetryType:null,errorHistory:[]});(0,a.useRef)(tB);let[t4,t8]=(0,a.useState)(!1),[re,rt]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[rr,rn]=(0,a.useState)(!1),[ro,rs]=(0,a.useState)(!1),[ra,ri]=(0,a.useState)(!1),[rl,rc]=(0,a.useState)(""),[rm,rd]=(0,a.useState)(!1),[ru,rp]=(0,a.useState)(!1),[rb,rg]=(0,a.useState)(!1),rf=e=>{try{localStorage.setItem("favoriteVoices",JSON.stringify({favoriteVoiceIds:e,lastUpdated:Date.now()}))}catch(e){console.error("无法保存收藏到 localStorage:",e)}},rx=e=>{let t=tC.includes(e);tA(t=>{let r=t.includes(e)?t.filter(t=>t!==e):[...t,e];return rf(r),r}),e2({title:t?"已取消收藏":"已添加收藏",description:t?"声音已从收藏中移除":"声音已添加到收藏",duration:2e3})},[rh,ry]=(0,a.useState)(!1),[rw,rv]=(0,a.useState)(!1),[rk,rj]=(0,a.useState)(null),[rN,rz]=(0,a.useState)("single"),[rC,rA]=(0,a.useState)([{id:1,voice:"",text:""}]),[rS,rT]=(0,a.useState)(null),[r$,rE]=(0,a.useState)(null),[rL,rI]=(0,a.useState)(!1),rM=(0,a.useRef)(null),rR=(0,a.useRef)(null),rD=(0,a.useRef)(null),rP=(0,a.useRef)(null),r_=(0,a.useRef)(null);(0,a.useRef)(null);let rU=(0,a.useRef)(null);(0,a.useRef)(eE);let rF=(0,a.useRef)(!1),rO=(0,a.useRef)(null),rB=(0,a.useRef)(null),rH=(0,a.useMemo)(()=>"eleven_v3"===I?3e3:5e3,[I]),rK=(0,a.useMemo)(()=>{let t=e;if("all"!==tw&&(t=t.filter(e=>e.gender===tw)),"all"!==tk&&(t=t.filter(e=>e.language===tk)),th.trim()){let e=th.toLowerCase().trim();t=t.filter(t=>t.name.toLowerCase().includes(e)||t.description.toLowerCase().includes(e))}return tS&&(t=t.filter(e=>tC.includes(e.id))),t},[e,tw,tk,th,tS,tC]),rW=(0,a.useMemo)(()=>{let t=e;if("all"!==t$&&(t=t.filter(e=>e.gender===t$)),"all"!==tL&&(t=t.filter(e=>e.language===tL)),tM.trim()){let e=tM.toLowerCase().trim();t=t.filter(t=>t.name.toLowerCase().includes(e)||t.description.toLowerCase().includes(e))}return t_&&(t=t.filter(e=>tC.includes(e.id))),t},[e,t$,tL,tM,t_,tC]),rG=(0,a.useMemo)(()=>{let e=(d.match(/\[([a-zA-Z\s]+)\]/g)||[]).length;return 0===e?null:(0,n.jsxs)("div",{className:"absolute -top-8 right-2 bg-purple-100 text-purple-700 text-sm font-semibold px-2.5 py-1 rounded-lg z-10",children:["情感标注词 ",e,"个"]})},[d]),rq=()=>{if(tv(t$),tj(tL),tx(tM),tT(t_),tD)b(tD);else{let t=e.filter(e=>{if("all"!==t$&&e.gender!==t$||"all"!==tL&&e.language!==tL)return!1;if(tM.trim()){let t=tM.toLowerCase().trim();if(!e.name.toLowerCase().includes(t)&&!e.description.toLowerCase().includes(t))return!1}return!t_||!!tC.includes(e.id)});t.length>0&&!t.find(e=>e.id===p)&&b(t[0].id)}tz(!1)},rV=[{id:"eleven_v3",name:"Eleven v3 (测试)支持情感标注慢速",description:"最具表现力的模型，支持70多种语言。相比以往的模型，需要更多的提示工程。目前处于 Alpha 阶段，可靠性将随着时间推移不断提升",icon:(0,n.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,n.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"#5D79DF",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,n.jsx)("path",{d:"M16.7527 21.9441C16.6291 22.2951 16.1328 22.2951 16.0093 21.9441L14.9179 18.8442C14.8458 18.6395 14.6219 18.5316 14.417 18.6027L11.4214 19.642C11.0768 19.7615 10.7687 19.3892 10.9506 19.0731L12.6191 16.1737C12.7216 15.9955 12.6699 15.7684 12.5003 15.6521L9.7946 13.7969C9.4974 13.5931 9.6069 13.1303 9.9639 13.0814L13.1369 12.6469C13.3456 12.6183 13.4949 12.4307 13.4759 12.2209L13.1743 8.8874C13.141 8.519 13.5876 8.3118 13.8474 8.5751L16.1004 10.8595C16.2547 11.016 16.5072 11.016 16.6616 10.8595L18.9146 8.5751C19.1743 8.3118 19.621 8.519 19.5877 8.8874L19.2861 12.2209C19.2671 12.4307 19.4164 12.6183 19.6251 12.6469L22.798 13.0814C23.155 13.1303 23.2646 13.5931 22.9674 13.7969L20.2616 15.6521C20.0921 15.7684 20.0404 15.9955 20.1429 16.1737L21.8113 19.0731C21.9932 19.3892 21.6852 19.7615 21.3406 19.642L18.345 18.6027C18.14 18.5316 17.9161 18.6395 17.8441 18.8442L16.7527 21.9441Z",stroke:"white",strokeWidth:"1.5",fill:"none"})]})},{id:"eleven_multilingual_v2",name:"Eleven Multilingual v2",description:"最具真实感、情感丰富的模式，支持29种语言。非常适合配音、有声书、后期制作或其他内容创作需求",icon:(0,n.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,n.jsx)("defs",{children:(0,n.jsxs)("linearGradient",{id:"multilingual-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,n.jsx)("stop",{offset:"0%",stopColor:"#F99BFF"}),(0,n.jsx)("stop",{offset:"100%",stopColor:"#9B59FF"})]})}),(0,n.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#multilingual-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,n.jsx)("path",{d:"M12 10C13.5 10 14.5 11 14.5 12.5V13.5C14.5 14 14.8 14.3 15.3 14.3C15.8 14.3 16.1 14 16.1 13.5V12C16.1 10.3 14.8 9 13.1 9C11.4 9 10.1 10.3 10.1 12V16C10.1 17.7 11.4 19 13.1 19H13.5",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M13.5 16.5C13.8 16.8 14.2 17 14.7 17",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M18 12C18.5 12.5 18.8 13.2 18.8 14C18.8 14.8 18.5 15.5 18 16",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M20 10C21 11 21.5 12.4 21.5 14C21.5 15.6 21 17 20 18",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M22 8C23.5 9.5 24.2 11.6 24.2 14C24.2 16.4 23.5 18.5 22 20",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"})]})},{id:"eleven_turbo_v2_5",name:"Eleven Turbo v2.5",description:"高质量、低延迟模型支持32种语言。非常适合在需要速度且需要使用非英语语言的使用场景",icon:(0,n.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,n.jsx)("defs",{children:(0,n.jsxs)("linearGradient",{id:"turbo-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,n.jsx)("stop",{offset:"0%",stopColor:"#10B981"}),(0,n.jsx)("stop",{offset:"100%",stopColor:"#059669"})]})}),(0,n.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#turbo-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,n.jsx)("path",{d:"M18 8L12 16H16L14 24L20 16H16L18 8Z",fill:"white"}),(0,n.jsx)("path",{d:"M22 10H26",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,n.jsx)("path",{d:"M23 13H25",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,n.jsx)("path",{d:"M22 19H26",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,n.jsx)("path",{d:"M23 22H25",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"})]})},{id:"eleven_turbo_v2",name:"Eleven Turbo v2",description:"仅支持英文的低延迟模型，在需要速度且仅需英文的情况下表现最佳。性能与 Turbo v2.5 相当",icon:(0,n.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,n.jsx)("defs",{children:(0,n.jsxs)("linearGradient",{id:"turbo-v2-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,n.jsx)("stop",{offset:"0%",stopColor:"#3B82F6"}),(0,n.jsx)("stop",{offset:"100%",stopColor:"#1D4ED8"})]})}),(0,n.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#turbo-v2-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,n.jsx)("path",{d:"M16 6L18 12H20L16 26L12 12H14L16 6Z",fill:"white"}),(0,n.jsx)("path",{d:"M14 12C13.5 13 13.5 14 14 15",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round"}),(0,n.jsx)("path",{d:"M18 12C18.5 13 18.5 14 18 15",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round"}),(0,n.jsx)("text",{x:"16",y:"21",textAnchor:"middle",fill:"white",fontSize:"6",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"EN"})]})}],rZ=(0,a.useMemo)(()=>"dialogue"===rN?rV.filter(e=>"eleven_v3"===e.id):rV,[rN]);(0,a.useRef)(I);let rJ=(0,a.useMemo)(()=>!!g||!!t7.frontendInProgress||("single"===rN?!d.trim()||!p:"dialogue"!==rN||rC.some(e=>!e.text.trim()||!e.voice)),[rN,g,t7.frontendInProgress,d,p,rC]),rY=async e=>{rz(e)},rX=(e,t,r)=>{rA(rC.map(n=>n.id===e?{...n,[t]:r}:n))},rQ=async(e,t)=>{try{if("replace"===t)rA(e),e.length>0&&(b(e[0].voice),rT(e[0].id));else{let t=[...rC,...e];rA(t),e.length>0&&(b(e[0].voice),rT(e[0].id),setTimeout(()=>{let t=document.querySelector(`[data-dialogue-line-id="${e[0].id}"]`);t&&t.closest(".overflow-y-auto")&&t.scrollIntoView({behavior:"smooth",block:"center"})},100))}}catch(e){throw console.error("批量导入失败:",e),e}},r0=async()=>{try{await eF.j2.logout(),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},r5=async()=>{if(!d.trim()){rj("请先输入要处理的文本");return}if(!rw){if(!eF.j2.isLoggedIn()){rj("请先登录后使用自动标注功能"),eV(!0);return}if(!tb.isVip){rj("自动标注功能需要会员权限，请先开通会员");return}if(Date.now()>tb.expireAt){rj("会员已过期，请续费后使用自动标注功能");return}rv(!0),rj(null);try{let e=await eF.lY.processText(d.trim(),"auto");if(!e.success||!e.processedText)throw Error("服务返回的数据格式不正确");u(e.processedText),e.rateLimit&&console.log(`自动标注剩余次数: ${e.rateLimit.remaining}`)}catch(t){console.error("Auto generate tags error:",t);let e="自动生成标签失败，请稍后重试";"TypeError"===t.name&&t.message.includes("fetch")?e="网络连接失败，请检查网络后重试":t.message.includes("登录")?(e=t.message,eV(!0)):t.message.includes("会员")||t.message.includes("权限")?e=t.message:t.message.includes("频繁")?e=t.message:t.message&&(e=t.message),rj(e)}finally{rv(!1)}}},r1=(0,a.useCallback)((e,t)=>{tp(`输入内容超过${t}字符限制，原内容${e}字符，已自动截取到${t}字符`),td(!0),setTimeout(()=>{td(!1)},3e3)},[]);if(r)return(0,n.jsx)(eX,{error:r,onRetry:c});let r2=async()=>{if(rc(""),!re.currentPassword||!re.newPassword||!re.confirmPassword){rc("请填写所有密码字段");return}if(re.newPassword!==re.confirmPassword){rc("新密码和确认密码不匹配");return}if(re.newPassword.length<6){rc("新密码长度不能少于6位");return}if(re.currentPassword===re.newPassword){rc("新密码不能与当前密码相同");return}rd(!0);try{await eF.j2.changePassword({currentPassword:re.currentPassword,newPassword:re.newPassword}),t8(!1),rt({currentPassword:"",newPassword:"",confirmPassword:""}),rc(""),rp(!0)}catch(e){console.error("Change password error:",e),rc(e.message||"修改密码失败，请重试")}finally{rd(!1)}},r3=(0,a.useCallback)(e=>{t2(e),t3.current=e;try{e?sessionStorage.setItem(t6,JSON.stringify(e)):sessionStorage.removeItem(t6)}catch(e){console.error("[TASK-DATA-SAFE] Failed to save to sessionStorage:",e)}},[t6]),r6=(0,a.useCallback)(()=>{if(t1)return t1;if(t3.current)return t2(t3.current),t3.current;try{let e=sessionStorage.getItem(t6);if(e){let t=JSON.parse(e);return t2(t),t3.current=t,t}}catch(e){console.error("[TASK-DATA-SAFE] Failed to recover from sessionStorage:",e)}return null},[t1,t6]),r7=(0,a.useCallback)(e=>{t9(t=>{let r=e(t);try{sessionStorage.setItem("retry_state",JSON.stringify({frontendAttempts:r.frontendAttempts,datacenterAttempts:r.datacenterAttempts,frontendStartTime:r.frontendStartTime,errorHistory:r.errorHistory.slice(-10)}))}catch(e){console.error("[RETRY-STATE] Failed to persist retry state:",e)}return r})},[]);(0,a.useCallback)(()=>{try{let e=sessionStorage.getItem("retry_state");if(e){let t=JSON.parse(e);r7(e=>({...e,frontendAttempts:t.frontendAttempts||0,datacenterAttempts:t.datacenterAttempts||0,frontendStartTime:t.frontendStartTime||0,errorHistory:t.errorHistory||[]}))}}catch(e){console.error("[RETRY-STATE] Failed to recover retry state:",e)}},[r7]);let r9=(0,a.useCallback)(()=>{try{let e=sessionStorage.getItem(tY);if(e){let t=JSON.parse(e);if(Date.now()-t.createdAt<36e5)return t}return sessionStorage.removeItem(tY),null}catch(e){return console.error("[TASK-MAPPING] Failed to get task mapping:",e),null}},[]),r4=(0,a.useCallback)(e=>{try{sessionStorage.setItem(tY,JSON.stringify(e)),console.log("[TASK-MAPPING] Saved mapping to sessionStorage:",e)}catch(e){console.error("[TASK-MAPPING] Failed to save task mapping:",e)}},[]),r8=(0,a.useCallback)(e=>{let t=r9();return t&&t.displayTaskId===e?(console.log("[TASK-MAPPING] Found mapping for refresh:",{displayTaskId:e,physicalTaskIds:t.physicalTaskIds}),t.physicalTaskIds):(console.log("[TASK-MAPPING] No mapping found for refresh, using original ID:",e),[e])},[r9]),ne={content_violation:"我们深表歉意，您使用的文本可能违反了Elevenlabs的服务条款，因此已被屏蔽。(此信息由Elevenlabs官方返回)",quota_exceeded:"您的配额已用完，请充值后继续使用。",authentication_failed:"登录会话已过期，请重新登录。",rate_limit_exceeded:"请求过于频繁，请稍后再试。",system_error:"系统暂时繁忙，请稍后再试。",permission_denied:"您没有执行此操作的权限。",invalid_input:"输入参数无效，请检查后重试。"},nt=(0,a.useCallback)(e=>void 0!==e.errorType&&void 0!==e.isRetryable?{message:e.message||"",errorType:e.errorType||null,isRetryable:e.isRetryable||!1,isStructured:!0}:{message:e.message||"",errorType:null,isRetryable:!1,isStructured:!1},[]);(0,a.useCallback)((e,t)=>e&&ne[e]?ne[e]:t,[]);let nr=(0,a.useCallback)((e,t,r)=>{if(void 0!==t&&void 0!==r)return!0===r;let n=e.toLowerCase();return["too many chunks failed","chunks failed even after retry","system issue","temporary failure","processing timeout","internal server error","service temporarily unavailable","connection reset","request timeout","network error","service unavailable"].some(e=>n.includes(e))},[]),nn=(0,a.useCallback)((e,t)=>{if(t)return["content_violation","quota_exceeded","authentication_failed","permission_denied","invalid_input"].includes(t);let r=e.toLowerCase();return["会员权限不足","配额不足","登录会话已过期","参数验证失败","文本内容违规","非会员用户","token","登录","权限","配额","会员"].some(e=>r.includes(e))},[]),no=(0,a.useCallback)(()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,11);return`logical_${e}_${t}`},[]),ns=(0,a.useCallback)((e,t)=>{let r=rB.current;r&&rO.current&&(rO.current.updateTaskStatus(r,e,t),console.log("[LOGICAL-TASK] Updated task status:",r,"status:",e))},[]),na=(0,a.useCallback)(()=>{tq(null),tJ.current=null,tZ([]);try{sessionStorage.removeItem(tY),sessionStorage.removeItem("current_logical_task_id"),sessionStorage.removeItem("logical_task_timestamp")}catch(e){console.warn("[TASK-MAPPING] Failed to clear sessionStorage:",e)}console.log("[TASK-MAPPING] Cleared task mapping and logical task state")},[]),ni=(0,a.useCallback)((e,t="",r=-1)=>{let n;let o="https:"===window.location.protocol?"wss:":"ws:",s=new URL(n="https://myaitts-worker.panxuchao19951206.workers.dev").host,a="dialogue"===e?"/api/tts/ws/dialogue/generate":"/api/tts/ws/generate",i=n?.replace("https://","wss://").replace("http://","ws://");return i?`${i}${a}${t}`:(console.error("API URL is not configured!"),`${o}//${s}${a}${t}`)},[]),nl=(0,a.useCallback)(()=>0,[]);(0,a.useCallback)(()=>{let e=nl();r7(t=>({...t,maxBackupIndex:e>0?e-1:-1}))},[nl]);let nc=(0,a.useCallback)(e=>{let t={action:"start",token:eO.tC.getAccessToken(),model:e.model};return"dialogue"===e.taskType?(t.taskType="dialogue",t.dialogue=e.dialogueLines):(t.input=e.input,t.voice=e.voice),t.stability=e.stability,t.similarity_boost=e.similarity_boost,t.speed=e.speed,"eleven_turbo_v2"!==e.model&&"eleven_turbo_v2_5"!==e.model&&(t.style=e.style),t},[]),nm=(0,a.useCallback)(async e=>{if(t7.isLocked&&"frontend"!==t7.activeRetryType)return;let t=!1,r=0;if(r7(n=>{let o=n.frontendAttempts+1;return o>n.frontendMaxAttempts?{...n,frontendInProgress:!1,isLocked:!1,activeRetryType:null}:(t=!0,r=o,{...n,frontendAttempts:o,frontendInProgress:!0,isLocked:!0,lockReason:`frontend_retry_attempt_${o}`,activeRetryType:"frontend",frontendStartTime:n.frontendStartTime||Date.now(),errorHistory:[...n.errorHistory,{timestamp:Date.now(),type:"system_temp",message:e,retryType:"frontend"}]})}),!t){f(!1),tH("failed"),tW("多次重试失败"),eR("系统暂时繁忙，请稍后再试或联系客服"),r7(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null}));return}try{tW(`正在重试... (${r}/${t7.frontendMaxAttempts})`),await new Promise(e=>setTimeout(e,3e3));let e=r6();if(!e)throw Error("原始任务数据丢失");let t="dialogue"===e.taskType?"dialogue":"single",n=ni(t),o=new WebSocket(n);np(o,{retryType:"frontend",attemptNumber:r,maxAttempts:t7.frontendMaxAttempts}),o.onopen=()=>{let t=nc(e);o.send(JSON.stringify(t))}}catch(e){console.error("[FRONTEND-RETRY] Failed to create retry connection:",e),f(!1),tH("failed"),eR("重试连接失败，请稍后再试"),r7(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null}))}},[t7,r7,r6,ni,nc]),nd=(0,a.useCallback)(async e=>{let t="https://myaitts-worker.panxuchao19951206.workers.dev".split(",").map(e=>e.trim()).filter(e=>e.length>0);if(0===t.length){console.log("[BACKUP-API] No valid backup APIs found"),f(!1),tH("failed"),tW("重试失败"),eR("系统暂时繁忙，请稍后再试或联系客服");return}if(t7.isLocked&&"backup"!==t7.activeRetryType)return;let r=!1,n=-1,o=t7.currentBackupIndex+1;if(o>=t.length){console.log(`[BACKUP-API] All backup APIs exhausted (${t.length} tried)`),f(!1),tH("failed"),tW("所有备用服务器均不可用"),eR("主备服务器均不可用，请稍后再试或联系客服"),r7(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null}));return}if(r7(t=>(r=!0,n=o,{...t,currentBackupIndex:o,usingBackupApi:!0,isLocked:!0,lockReason:`backup_api_${o}_retry`,activeRetryType:"backup",backupApiStartTime:t.backupApiStartTime||Date.now(),errorHistory:[...t.errorHistory,{timestamp:Date.now(),type:`backup_api_${o}`,message:e,retryType:"backup"}]})),r)try{let e=t[n];tW(`正在尝试备用服务器 ${n+1}/${t.length}...`),await new Promise(e=>setTimeout(e,2e3));let r=r6();if(!r)throw Error("原始任务数据丢失");let o="dialogue"===r.taskType?"dialogue":"single",s=ni(o,"",n);console.log(`[BACKUP-API] Attempting backup API ${n+1}/${t.length}: ${e}`);let a=new WebSocket(s);np(a,{retryType:"backup",attemptNumber:n+1,maxAttempts:t.length}),a.onopen=()=>{let e=nc(r);a.send(JSON.stringify(e))}}catch(r){console.error(`[BACKUP-API-RETRY] Failed to create backup API connection (index ${n}):`,r),n+1<t.length?(console.log("[BACKUP-API] Trying next backup API..."),setTimeout(()=>{nd(e)},1e3)):(f(!1),tH("failed"),eR("所有备用API连接失败，请稍后再试"),r7(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null})))}},[t7,r7,r6,ni,nc]),nu=async(e,t)=>{let r=tX+1;if(r>1){f(!1),tH("failed"),tW("多次重试失败"),eR("检查网络环境，请稍后再试");return}tQ(r),t5(t=>[...t,...e]),tW("正在重试...");try{let n=[...t0,...e],o=n.length>0?`?excludeLocations=${n.join(",")}`:"",s=t?.taskType==="dialogue"?"dialogue":"single",a=ni(s,o),i=new WebSocket(a);i.onopen=()=>{i.send(JSON.stringify({action:"retry",recoveryData:t||t1,retryCount:r,excludedLocations:n}))},np(i,{retryType:"datacenter",attemptNumber:r,maxAttempts:1})}catch(e){console.error("[RETRY] Failed to create retry connection:",e),f(!1),tH("failed"),eR("重试连接失败，请稍后再试")}},np=(0,a.useCallback)((e,t)=>{let r=!!t;e.onmessage=n=>{try{let o=JSON.parse(n.data);switch(o.type){case"initialized":if(o.taskId){let e=o.taskId;tO(e),rB.current=e,tZ(t=>t.includes(e)?t:[...t,e]);let t=tJ.current;if(t){let r=r9();r&&r.logicalTaskId===t?r.physicalTaskIds.includes(e)||(r.physicalTaskIds.push(e),console.log("[TASK-MAPPING] Added physical task to mapping:",r)):(r={logicalTaskId:t,displayTaskId:e,physicalTaskIds:[e],createdAt:Date.now()},rO.current&&rO.current.addTask(r.displayTaskId),console.log("[TASK-MAPPING] New task created:",r)),r4(r)}}tH("processing"),tW("连接成功，任务准备就绪...");break;case"progress":o.message&&tW(o.message);break;case"complete":f(!1),tH("complete"),tW("音频生成完成！"),h(!0),G({streamUrl:o.streamUrl||null,downloadUrl:o.downloadUrl||null,secureStreamUrl:null}),o.streamUrl&&e3(o.streamUrl).then(e=>{e?(G(t=>({...t,secureStreamUrl:e})),rU.current&&(rU.current.src=e),console.log("[SECURE-AUDIO] Audio loaded and ready for playback")):eR("音频加载失败，请重试")});let s=o.taskId||rB.current;if(s){let e=r9();e&&e.physicalTaskIds.includes(s)?(rO.current&&rO.current.updateTaskStatus(e.displayTaskId,"complete",o.downloadUrl),console.log(`[TASK-MAPPING] Task complete. Updating display task '${e.displayTaskId}' for physical task '${s}'.`)):(rO.current&&rO.current.updateTaskStatus(s,"complete",o.downloadUrl),console.warn(`[TASK-MAPPING] Could not find mapping for completed task '${s}'. Updating directly.`))}ns("complete",o.downloadUrl),r7(e=>({...e,frontendAttempts:0,datacenterAttempts:0,backupApiAttempts:0,frontendInProgress:!1,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null,frontendStartTime:0,backupApiStartTime:0,errorHistory:[]})),r3(null),na(),na(),e.close();break;case"error":let a=nt(o),i=(0,eH.zf)({message:a.message,errorType:a.errorType,code:a.errorType});if(console.error("[WebSocket] Generation Error:",{original:a.message,errorType:a.errorType,isRetryable:a.isRetryable,isStructured:a.isStructured,userFriendly:i}),r){if("frontend"===t.retryType){if(nr(a.message,a.errorType,a.isRetryable)&&t.attemptNumber<t.maxAttempts){e.close(),setTimeout(()=>{nm(a.message)},1e3);return}{f(!1),tH("failed"),tW("多次重试失败"),eR("系统暂时繁忙，请稍后再试或联系客服");let t=r9();t&&rO.current&&(rO.current.updateTaskStatus(t.displayTaskId,"failed"),console.log("[TASK-MAPPING] Frontend retry failed, updated display task:",t.displayTaskId)),ns("failed"),r7(e=>({...e,frontendInProgress:!1,usingBackupApi:!1,isLocked:!1,activeRetryType:null})),na(),e.close();return}}if("backup"===t.retryType){let t="https://myaitts-worker.panxuchao19951206.workers.dev".split(",").map(e=>e.trim()).filter(e=>e.length>0)||[],r=t7.currentBackupIndex+1<t.length;if(nr(a.message,a.errorType,a.isRetryable)&&r){console.log(`[BACKUP-API] Current backup API failed, trying next one (${t7.currentBackupIndex+2}/${t.length})`),e.close(),setTimeout(()=>{nd(a.message)},1e3);return}{f(!1),tH("failed"),tW("所有备用服务器均不可用"),eR("主备服务器均不可用，请稍后再试或联系客服");let t=r9();t&&rO.current&&(rO.current.updateTaskStatus(t.displayTaskId,"failed"),console.log("[TASK-MAPPING] All backup APIs failed, updated display task:",t.displayTaskId)),ns("failed"),r7(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null})),na(),e.close();return}}}nn(a.message,a.errorType)?(f(!1),tH("failed"),tW("生成失败"),rF.current=!1,ns("failed"),console.log("[LOGICAL-TASK] User related error, logical task:",tG||tJ.current),r7(e=>({...e,frontendAttempts:0,datacenterAttempts:0,frontendInProgress:!1,isLocked:!1,activeRetryType:null,frontendStartTime:0})),r3(null),na(),eR(i),"authentication_failed"===a.errorType||a.message.toLowerCase().includes("token")||a.message.toLowerCase().includes("登录")?eV(!0):("quota_exceeded"===a.errorType||a.message.toLowerCase().includes("会员")||a.message.toLowerCase().includes("quota"))&&eP(!0)):nr(a.message,a.errorType,a.isRetryable)&&!t7.frontendInProgress?t7.frontendAttempts<t7.frontendMaxAttempts?(e.close(),setTimeout(()=>{nm(a.message)},1e3)):(f(!1),tH("failed"),tW("系统重试失败"),eR("系统暂时繁忙，请稍后再试或联系客服"),ns("failed"),console.log("[LOGICAL-TASK] System retry failed, logical task:",tG||tJ.current)):(f(!1),tH("failed"),tW("生成失败"),rF.current=!1,ns("failed"),console.log("[LOGICAL-TASK] Other error, logical task:",tG||tJ.current),r7(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null})),na(),eR(i||"音频生成失败，请稍后再试！【可尝试切换“梯子”地区】"));break;case"error_retryable":tW("正在重试..."),e.close(),setTimeout(()=>{nu(o.excludeLocations||[],o.taskData)},1e3);break;default:console.warn("[WebSocket] Unknown message type:",o.type)}}catch(e){console.error("[WebSocket] Failed to parse message:",e),eR("收到无法解析的数据")}},e.onclose=()=>{console.log("[WebSocket] Connection closed, isRetryConnection:",r,"retryContext:",t),(!r||t&&t.attemptNumber>=t.maxAttempts||"complete"===tB)&&(g&&(f(!1),console.log("[WebSocket] Reset isGenerating on close")),setTimeout(()=>{"complete"!==tB&&tW("")},3e3))},e.onerror=e=>{console.error("[WebSocket] Connection error:",e),r||(f(!1),eR("连接失败，请检查网络后重试"))}},[r7,r3,nn,nr,t7.frontendInProgress,nm]),nb=async()=>{if(rU.current){if(y)rU.current.pause(),w(!1);else try{if(W.secureStreamUrl)rU.current.src!==W.secureStreamUrl&&(rU.current.src=W.secureStreamUrl);else if(W.streamUrl){console.log("[PLAYBACK] Loading secure audio for playback...");let e=await e3(W.streamUrl);if(e)G(t=>({...t,secureStreamUrl:e})),rU.current.src=e;else throw Error("Failed to load secure audio")}else throw Error("No audio URL available");await rU.current.play(),w(!0)}catch(e){console.error("Manual playback failed:",e),w(!1),eR("播放失败，请检查网络连接或重新生成")}}},ng=async()=>{if(W.downloadUrl)try{let e=new Date,t=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0"),i=`tts_${t}${r}${n}_${o}${s}${a}.mp3`;await e6(W.downloadUrl,i)&&console.log("[DOWNLOAD] Download completed successfully with secure authentication")}catch(e){console.error("[DOWNLOAD] Download failed:",e),eR("下载失败，请重试")}},nf=(e,t)=>{if(!rU.current||!x)return 0;let r=t.getBoundingClientRect(),n=e-r.left,o=r.width,s=Math.max(0,Math.min(n,o)),a=o>0?s/o*100:0;e$(a);let i=rU.current.duration;if(i>0&&!isNaN(i)){let e=Math.max(0,Math.min(a/100*i,i)),t=Math.floor(e/60),r=Math.floor(e%60);ez(`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`)}return a},nx=e=>{if(!rU.current||!x)return;let t=rU.current.duration;if(t>0&&!isNaN(t)){let r=Math.max(0,Math.min(e/100*t,t));try{rU.current.currentTime=r}catch(e){console.warn("更新音频时间失败:",e)}}},nh=async(e,t)=>{if(e)try{if(eZ){eZ.pause(),eZ.currentTime=0;let e=eY!==t;if(eJ(null),eQ(null),!e)return}let r=new Audio(e);eJ(r),eQ(t),r.addEventListener("ended",()=>{eJ(null),eQ(null)}),r.addEventListener("error",()=>{console.error("Preview audio failed to load:",e),eJ(null),eQ(null)}),await r.play()}catch(e){console.error("Preview audio failed:",e),eJ(null),eQ(null)}},ny=[{left:15,top:20,duration:10},{left:75,top:35,duration:12},{left:45,top:60,duration:9},{left:85,top:15,duration:11},{left:25,top:80,duration:8},{left:65,top:45,duration:13}];return(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 p-6 relative overflow-hidden",children:[e0&&(0,n.jsx)(()=>(0,n.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:ny.map((e,t)=>(0,n.jsx)("div",{className:"animate-optimized absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float",style:{left:`${e.left}%`,top:`${e.top}%`,animationDelay:`${2*t}s`,animationDuration:`${e.duration}s`}},t))}),{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" animate-optimized absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-blue-200/50 to-purple-200/40 rounded-full blur-3xl animate-pulse"}),(0,n.jsx)("div",{style:{animationDelay:"2s"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" animate-optimized absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-200/50 to-pink-200/40 rounded-full blur-3xl animate-pulse"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`page-transition-optimized max-w-7xl mx-auto transition-all duration-1000 ${P?"opacity-100 translate-y-0":"opacity-0 translate-y-8"}`,children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mb-6",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-40 flex items-center justify-between mb-4 p-4 bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-100",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md opacity-50"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg",children:(0,n.jsx)(ee.A,{className:"w-6 h-6 text-white"})})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:[(0,n.jsx)("h1",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gradient-optimized text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent animate-gradient",children:"AI 语音工作室"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-0.5 w-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-1"})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 lg:gap-4",children:[(0,n.jsx)(e1,{ref:rO,getActualTaskIds:r8}),(0,n.jsxs)(i.$,{onClick:()=>window.location.href="/recharge",className:"group relative bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white px-3 py-2 lg:px-5 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 flex items-center gap-1 lg:gap-2 text-sm lg:text-base font-semibold overflow-hidden border border-white/20 backdrop-blur-sm mr-1 lg:mr-4",children:[(0,n.jsx)("div",{style:{animation:"breathe 3s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsx)("div",{style:{animation:"breathe-glow 4s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 opacity-0 group-hover:opacity-30 blur-sm transition-all duration-500 -z-10"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-4 h-4 lg:w-6 lg:h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 group-hover:rotate-12 group-hover:scale-110",children:[(0,n.jsx)("div",{style:{animation:"breathe 2.5s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-yellow-300 to-orange-300 rounded-full opacity-60 group-hover:opacity-80 transition-opacity duration-300"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative text-xs lg:text-sm font-bold text-white drop-shadow-sm",children:"\xa5"}),(0,n.jsx)("div",{style:{animation:"breathe 2s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative flex flex-col items-start",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" hidden sm:inline text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300",children:"充值中心"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" sm:hidden text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300",children:"充值"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-300 to-orange-300 group-hover:w-full transition-all duration-500 rounded-full"})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:[void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{style:{left:`${20+25*t}%`,top:`${30+15*t}%`,animation:`breathe ${1.5+.3*t}s ease-in-out infinite`,animationDelay:`${.2*t}s`},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute w-1 h-1 bg-yellow-300 rounded-full"},t))}),(0,n.jsx)("div",{style:{animation:"breathe 3.5s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-2xl bg-white/10 scale-0 group-hover:scale-100 opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out"})]}),(0,n.jsxs)("div",{ref:r_,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsxs)(i.$,{onClick:()=>B(!O),variant:"outline",className:"group relative bg-gradient-to-br from-white/95 via-gray-50/90 to-white/95 backdrop-blur-xl border-2 border-gray-200/60 hover:border-indigo-300/70 hover:bg-gradient-to-br hover:from-indigo-50/80 hover:via-purple-50/70 hover:to-blue-50/80 px-3 py-2 lg:px-4 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 flex items-center gap-2 lg:gap-3 transform hover:scale-105 hover:-translate-y-0.5 overflow-hidden min-w-[160px] max-w-[200px] lg:min-w-[180px] lg:max-w-[240px]",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-shimmer"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-4 h-4 lg:w-6 lg:h-6 bg-gradient-to-br from-indigo-500 via-purple-500 to-blue-600 rounded-full flex items-center justify-center group-hover:rotate-12 transition-all duration-500 shadow-md group-hover:shadow-lg",children:[(0,n.jsx)(et.A,{className:"w-2.5 h-2.5 lg:w-3.5 lg:h-3.5 text-white drop-shadow-sm"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -top-0.5 -right-0.5 w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border border-white shadow-sm",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75"})})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative hidden md:flex flex-col items-start flex-1 min-w-0",children:[(0,n.jsx)("span",{title:U,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm lg:text-base font-semibold bg-gradient-to-r from-gray-700 via-indigo-700 to-purple-700 bg-clip-text text-transparent group-hover:from-indigo-600 group-hover:via-purple-600 group-hover:to-blue-600 transition-all duration-500 leading-tight w-full truncate overflow-hidden text-ellipsis whitespace-nowrap",children:U}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -bottom-0.5 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 via-purple-400 to-blue-400 group-hover:w-full transition-all duration-700 rounded-full"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xs text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 font-medium",children:"在线"})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative ml-1",children:(0,n.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`w-3 h-3 lg:w-4 lg:h-4 text-gray-400 group-hover:text-indigo-500 transition-all duration-500 ${O?"rotate-180":""}`,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M19 9l-7 7-7-7",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:[void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{style:{left:`${20+25*t}%`,top:`${30+15*t}%`,animationDelay:`${.2*t}s`,animationDuration:"2s"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute w-1 h-1 bg-indigo-400 rounded-full animate-bounce"},t))}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-500 -z-10"})]}),O&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-xl shadow-xl z-50 overflow-hidden",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2",children:[(0,n.jsxs)("button",{onClick:()=>{t8(!0),B(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-full flex items-center gap-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200 mb-1",children:[(0,n.jsx)(er,{className:"w-4 h-4"}),"修改密码"]}),(0,n.jsxs)("button",{onClick:r0,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-full flex items-center gap-3 px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200",children:[(0,n.jsx)(en.A,{className:"w-4 h-4"}),"退出登录"]})]})})]})]})]})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-6",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" lg:col-span-2 space-y-3 lg:space-y-6 order-2 lg:order-1",children:[(0,n.jsxs)(l.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsxs)(l.Wu,{className:"pt-4 px-4 pb-2 sm:pt-6 sm:px-6 sm:pb-3 lg:pt-8 lg:px-7 lg:pb-4 relative",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mb-2",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl",children:(0,n.jsx)(eo.A,{className:"w-6 h-6 text-blue-600"})}),(0,n.jsx)("h2",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gradient-optimized text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"文本转语音"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex flex-col items-end",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mb-2 inline-flex items-center gap-2 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200/50 text-orange-700 text-sm font-medium px-3 py-1.5 rounded-lg shadow-sm animate-pulse",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-orange-500 rounded-full animate-ping"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:'"多人对话"功能限时开放中！'})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative bg-gradient-to-r from-slate-100 via-gray-100 to-slate-100 p-1 rounded-2xl shadow-inner border border-gray-200/50 backdrop-blur-sm",children:[(0,n.jsx)("div",{style:{boxShadow:"single"===rN?"0 4px 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2)":"0 4px 20px rgba(147, 51, 234, 0.4), 0 2px 8px rgba(147, 51, 234, 0.2)"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`absolute top-1 bottom-1 w-[calc(50%-2px)] bg-gradient-to-r rounded-xl shadow-lg transition-all duration-500 ease-out transform ${"single"===rN?"left-1 from-blue-500 via-blue-600 to-indigo-600":"left-[calc(50%+2px)] from-purple-500 via-purple-600 to-pink-600"}`}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative flex items-center",children:[(0,n.jsx)("button",{onClick:()=>rY("single"),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ${"single"===rN?"text-white drop-shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10 flex items-center justify-center gap-2",children:[(0,n.jsx)(et.A,{className:"w-4 h-4"}),"单人模式"]})}),(0,n.jsxs)("button",{onClick:()=>rY("dialogue"),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ${"dialogue"===rN?"text-white drop-shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:["single"===rN&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-purple-400/30 animate-pulse-ring-outer pointer-events-none"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 animate-pulse-ring-middle pointer-events-none"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-purple-600/10 animate-pulse-ring-inner pointer-events-none"})]}),(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10 flex items-center justify-center gap-2",children:[(0,n.jsx)(es.A,{className:"w-4 h-4"}),"多人对话"]}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -top-1 -right-1 z-20 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg border border-white/20 transform scale-90 hover:scale-100 transition-transform duration-200",children:"PRO"})]})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 rounded-2xl pointer-events-none"})]})]})]}),"eleven_v3"===I&&(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mb-2 flex items-center justify-between",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" inline-flex items-center gap-2 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 text-purple-700 text-sm font-medium px-3 py-2 rounded-lg shadow-sm",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"✨ v3模型支持情感标注词 目前处于测试阶段"}),(0,n.jsx)("button",{onClick:()=>{let e=[{text:`Okay, you are NOT going to believe this. You know how I've been totally stuck? [frustrated sigh] I was ready to give up. Then last night, just doodling, this random phrase popped into my head. Typed it out and—bam—FLOODGATES. Everything clicked. Character, ending, all of it. I stayed up till 3 AM, typing like a maniac [laughs]. And it's GOOD. It finally feels alive. Like it has a soul. What was a chore now feels like... MAGIC. [happy gasp] I'm still buzzing!`,voiceId:"iP95p4xoKVk53GoZ742B"},{text:'In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the "burn it all down" kind - [exhales] he was gentle, wise, with eyes like old stars. [softly] Even the birds fell silent when he passed.',voiceId:"cgSgspJ2msm6clMCkdW9"}],t=e[eC];u(t.text),b(t.voiceId),eA((eC+1)%e.length)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-purple-100 hover:bg-purple-200 text-purple-800 text-xs font-semibold rounded transition-colors duration-200 hover:scale-105 transform",children:"查看示例"}),(0,n.jsxs)("button",{onClick:()=>ry(!0),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 hover:from-blue-200 hover:to-indigo-200 text-blue-800 text-xs font-semibold rounded transition-all duration-200 hover:scale-105 transform flex items-center gap-1",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"\uD83D\uDCA1"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"了解更多"})]})]}),"dialogue"===rN&&(0,n.jsx)(eL,{dialogueLines:rC,voices:e,onImport:rQ,className:"ml-auto"})]})]}),"single"===rN?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[rG,(0,n.jsx)(J,{value:d,onChange:u,onFocus:()=>$(!0),onBlur:()=>$(!1),placeholder:"请输入要转换的文本...",className:`min-h-[160px] sm:min-h-[200px] lg:min-h-[270px] max-h-[270px] overflow-y-auto text-base lg:text-lg leading-relaxed resize-none border-2 transition-all duration-500 bg-gradient-to-br from-white to-gray-50/50 p-4 outline-none scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ${T?"border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.01]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"}`,maxLength:rH,onMaxLengthExceeded:r1})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between mt-2",children:["eleven_v3"===I?(0,n.jsxs)("button",{onClick:r5,disabled:rw||!d.trim()||e0&&(!eF.j2.isLoggedIn()||!tb.isVip||Date.now()>tb.expireAt),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`group relative flex items-center gap-2 px-3 py-1 text-sm font-medium rounded-lg transition-all duration-300 ${rw||!d.trim()||e0&&(!eF.j2.isLoggedIn()||!tb.isVip||Date.now()>tb.expireAt)?"text-gray-400 bg-gray-100 border border-gray-200 cursor-not-allowed":"text-purple-600 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 hover:scale-105 hover:shadow-md"}`,children:[!rw&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300"}),rw?(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4 relative z-10",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4 border-2 border-purple-300 border-t-purple-600 rounded-full animate-spin"})}):(0,n.jsx)(ea.A,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover:scale-110"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10",children:rw?"生成中...":"自动标注"}),e0&&(!eF.j2.isLoggedIn()||!tb.isVip||Date.now()>tb.expireAt)&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center",children:(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-white text-xs",children:"!"})})]}):(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 px-3 py-1 text-sm text-gray-500 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,n.jsx)(ea.A,{className:"w-4 h-4 text-gray-400"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"自动标注功能仅在 Eleven v3 模型下可用"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`text-sm px-3 py-1 rounded-lg transition-all duration-300 ${d.length>.8*rH?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-600"}`,children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-mono",children:d.length})," / ",rH]})]}),e0&&(!eF.j2.isLoggedIn()||!tb.isVip||Date.now()>tb.expireAt)&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-amber-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-amber-500 rounded-full"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium",children:eF.j2.isLoggedIn()?tb.isVip?"会员已过期，请续费后使用":"自动标注功能需要会员权限":"请先登录后使用自动标注功能"})]})})]}):(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsx)(eU,{dialogueLines:rC,voices:e,voiceIconMapping:o,voiceIcons:m,activeDialogueLineId:rS,onSelectLine:e=>{rT(e),rE(e)},onUpdateText:(e,t)=>rX(e,"text",t),onRemoveLine:e=>{if(rC.length>1){let t=rC.filter(t=>t.id!==e);rA(t),rS===e&&t.length>0&&rT(t[0].id),r$===e&&(rE(null),L(!1))}},onTextInputFocus:e=>{rT(e),L(!1),rE(null)},onEditVoice:e=>{let t=rC.find(t=>t.id===e);t&&b(t.voice),rE(e),rT(e),L(!0)},containerHeight:350,virtualThreshold:20}),(0,n.jsx)("button",{onClick:()=>{if(rL)return;rI(!0);let t=Math.max(...rC.map(e=>e.id))+1;rA([...rC,{id:t,voice:e[0]?.id||"",text:""}]),rT(t),setTimeout(()=>{let e=document.querySelector(`[data-dialogue-line-id="${t}"]`);if(e){let t=e.closest(".overflow-y-auto");if(t){let r=t.getBoundingClientRect(),n=e.getBoundingClientRect(),o=t.scrollTop,s=n.top-r.top+o,a=r.height,i=n.height;t.scrollTo({top:Math.max(0,s-a/2+i/2),behavior:"smooth"})}else e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"});setTimeout(()=>{let t=e.querySelector('div[contenteditable="true"]');t&&t.focus(),rI(!1)},300)}else rI(!1)},100)},disabled:rL,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`w-full flex items-center justify-center gap-2 py-3 text-sm font-semibold border-2 border-dashed rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ${rL?"text-gray-400 bg-gray-50 border-gray-200 cursor-not-allowed":"text-blue-600 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:scale-102 hover:shadow-md focus:ring-blue-400"}`,children:rL?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),"添加中..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(ei,{className:"w-4 h-4"}),"添加对话"]})})]}),(eM||rk)&&(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mt-4 space-y-2",children:[eM&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`p-4 rounded-xl border ${eM.includes("违反")||eM.includes("屏蔽")||eM.includes("服务条款")?"bg-orange-50 border-orange-200":eM.includes("配额")||eM.includes("会员")||eM.includes("权限")?"bg-yellow-50 border-yellow-200":"bg-red-50 border-red-200"}`,children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`flex items-center gap-2 ${eM.includes("违反")||eM.includes("屏蔽")||eM.includes("服务条款")?"text-orange-700":eM.includes("配额")||eM.includes("会员")||eM.includes("权限")?"text-yellow-700":"text-red-700"}`,children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`w-2 h-2 rounded-full ${eM.includes("违反")||eM.includes("屏蔽")||eM.includes("服务条款")?"bg-orange-500":eM.includes("配额")||eM.includes("会员")||eM.includes("权限")?"bg-yellow-500":"bg-red-500"}`}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium",children:eM})]})}),rk&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-4 bg-orange-50 border border-orange-200 rounded-xl",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-orange-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-orange-500 rounded-full"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium",children:rk})]})})]})]})]}),!("eleven_v3"===I&&!x)&&(0,n.jsxs)(l.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 rounded-2xl overflow-hidden",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsx)(l.Wu,{className:"p-0 relative",children:x?(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:[(0,n.jsx)("audio",{ref:rU,src:W.secureStreamUrl||void 0,onEnded:()=>{w(!1),e$(0),ez("00:00"),rU.current&&(rU.current.currentTime=0)},style:{display:"none"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-gray-50 to-blue-50/30 px-6 py-5 pb-8 relative",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-4",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative flex items-center justify-center gap-6",children:[(0,n.jsx)(i.$,{onClick:()=>{rU.current&&(rU.current.currentTime=Math.max(0,rU.current.currentTime-10))},variant:"ghost",size:"sm",className:"w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,n.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4",children:(0,n.jsx)("path",{d:"M6 6h2v12H6zm3.5 6l8.5 6V6z",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})}),(0,n.jsx)(i.$,{onClick:nb,size:"sm",className:"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex-shrink-0 border-0 hover:scale-105",children:y?(0,n.jsx)(ec,{className:"w-5 h-5"}):(0,n.jsx)(em,{className:"w-5 h-5 ml-0.5"})}),(0,n.jsx)(i.$,{onClick:()=>{rU.current&&(rU.current.currentTime=Math.min(rU.current.duration||0,rU.current.currentTime+10))},variant:"ghost",size:"sm",className:"w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,n.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4",children:(0,n.jsx)("path",{d:"M16 18h2V6h-2zm-3.5-6L4 6v12z",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})}),(0,n.jsx)(i.$,{onClick:ng,variant:"ghost",size:"sm",className:"absolute right-0 w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,n.jsx)(ed,{className:"w-4 h-4"})})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative pb-6",children:[(0,n.jsxs)("div",{onClick:e=>{nx(nf(e.clientX,e.currentTarget))},onMouseDown:e=>{e.preventDefault(),eI(!0);let t=e.currentTarget,r=e=>{e.preventDefault(),requestAnimationFrame(()=>{nf(e.clientX,t)})},n=e=>{e.preventDefault(),nx(nf(e.clientX,t)),eI(!1),document.removeEventListener("mousemove",r),document.removeEventListener("mouseup",n),document.removeEventListener("mouseleave",n)};document.addEventListener("mousemove",r,{passive:!1}),document.addEventListener("mouseup",n,{passive:!1}),document.addEventListener("mouseleave",n,{passive:!1})},title:`拖拽或点击调整播放进度 (${X} / ${V})`,style:{cursor:eE?"grabbing":"pointer"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`relative bg-gray-200 rounded-full cursor-pointer transition-all duration-200 ${eE?"h-1.5":"h-1 hover:h-1.5"}`,children:[(0,n.jsx)("div",{style:{width:`${eS}%`,transition:eE?"none":"width 0.15s ease-out"},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-200"}),eS>0&&(0,n.jsx)("div",{style:{left:`${eS}%`,marginLeft:"-6px",cursor:eE?"grabbing":"grab",transition:eE?"none":void 0},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg ${eE?"scale-125 opacity-100":"opacity-0 hover:opacity-100 transition-all duration-150"}`})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-6 left-0 text-xs font-mono text-gray-600 font-medium",children:X}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-6 right-0 text-xs font-mono text-gray-600 font-medium",children:V})]})]})})]}):(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex flex-col items-center justify-center py-12 text-center px-6",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-14 h-14 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-3",children:(0,n.jsx)(el,{className:"w-7 h-7 text-blue-600"})}),(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg font-semibold text-gray-900 mb-2",children:"您的音频杰作将在此呈现"}),(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-500 text-sm",children:"选择声音并点击生成，创造完美的语音体验"})]})})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-2 lg:space-y-4 relative z-10 order-1 lg:order-2",children:[(0,n.jsxs)(l.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-30",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsxs)(l.Wu,{className:"p-6 relative",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 mb-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl",children:"dialogue"===rN&&null!==r$?(0,n.jsx)(es.A,{className:"w-5 h-5 text-purple-600"}):(0,n.jsx)(ee.A,{className:"w-5 h-5 text-green-600"})}),(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"dialogue"===rN&&null!==r$?"为对话行选择声音":"选择声音"})]}),(0,n.jsxs)("div",{ref:rM,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsxs)("button",{onClick:()=>L(!E),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ${E?"border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.02]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"}`,children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:p?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-6 h-6 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/trigger:scale-110",children:(0,n.jsx)("img",{src:o[p]||m[0],alt:e.find(e=>e.id===p)?.name,onError:t=>{let r=t.target;r.style.display="none";let n=r.parentElement;n&&(n.innerHTML=`
                                    <div class="w-full h-full rounded-full flex items-center justify-center text-white text-sm font-bold ${e.find(e=>e.id===p)?.gender==="male"?"bg-gradient-to-r from-blue-400 to-blue-600":e.find(e=>e.id===p)?.gender==="female"?"bg-gradient-to-r from-pink-400 to-purple-600":"bg-gradient-to-r from-purple-400 to-indigo-600"}">
                                      ${e.find(e=>e.id===p)?.name[0]||"?"}
                                    </div>
                                  `)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-full h-full object-cover"})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-gray-900 text-base",children:e.find(e=>e.id===p)?.name}),"dialogue"===rN&&null!==rS&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xs text-gray-500",children:null!==r$?`正在编辑: 对话 ${rC.findIndex(e=>e.id===r$)+1}`:`当前: 对话 ${rC.findIndex(e=>e.id===rS)+1}`})]})]}):(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-500 text-base",children:"请选择声音..."})}),(0,n.jsx)(Q,{className:`w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-blue-500 ${E?"rotate-180 text-blue-500":""}`})]}),E&&(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-4 border-b border-gray-100/60 bg-gradient-to-r from-gray-50/30 to-blue-50/20",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative group flex-1",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,n.jsx)(eu,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10"}),(0,n.jsx)("input",{ref:rD,type:"text",placeholder:"搜索声音名称或描述...",value:tf,onChange:e=>tx(e.target.value),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-full pl-11 pr-11 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"}),tf&&(0,n.jsx)("button",{onClick:()=>tx(""),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-red-500 hover:scale-110 transition-all duration-200 z-10",children:(0,n.jsx)(ep.A,{className:"w-4 h-4"})})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:(0,n.jsxs)("button",{onClick:()=>{tE(tw),tI(tk),tR(tf),tP(p),tU(tS),tz(!0)},title:"筛选选项",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`relative p-3 border-2 rounded-2xl transition-all duration-300 flex items-center justify-center group/filter shadow-sm hover:shadow-lg ${"all"!==tw||"all"!==tk||tf.trim()||tS?"border-purple-400 bg-gradient-to-r from-purple-50 to-pink-50 text-purple-600 shadow-purple-100/50":"border-gray-200 bg-white hover:border-gray-300 text-gray-500 hover:text-purple-500"}`,children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl opacity-0 group-hover/filter:opacity-100 transition-all duration-300"}),(0,n.jsx)(eb,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover/filter:scale-110"}),("all"!==tw||"all"!==tk||tf.trim())&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-2 border-white shadow-sm",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75"})})]})}),"all"!==tw||"all"!==tk||tf.trim()||tS?(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:(0,n.jsxs)("button",{onClick:()=>{tv("all"),tj("all"),tx(""),tT(!1)},title:"清除所有筛选条件",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-3 border-2 border-red-200 bg-gradient-to-r from-red-50 to-orange-50 text-red-600 rounded-2xl transition-all duration-300 flex items-center justify-center group/clear shadow-sm hover:shadow-lg hover:border-red-300 hover:from-red-100 hover:to-orange-100",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-red-500/5 to-orange-500/5 rounded-2xl opacity-0 group-hover/clear:opacity-100 transition-all duration-300"}),(0,n.jsx)(ep.A,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover/clear:scale-110"})]})}):(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:(0,n.jsxs)("button",{onClick:()=>tT(!0),title:"仅显示收藏声音",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-3 border-2 border-gray-200 bg-white hover:border-pink-300 text-gray-500 hover:text-pink-500 rounded-2xl transition-all duration-300 flex items-center justify-center group/favorite shadow-sm hover:shadow-lg hover:bg-gradient-to-r hover:from-pink-50 hover:to-red-50",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-pink-500/5 to-red-500/5 rounded-2xl opacity-0 group-hover/favorite:opacity-100 transition-all duration-300"}),(0,n.jsx)(eg,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover/favorite:scale-110"})]})})]})}),("all"!==tw||"all"!==tk||tf.trim()||tS)&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-4 py-2 bg-gradient-to-r from-blue-50/80 to-purple-50/60 border-t border-gray-100/60 animate-fade-in",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between text-xs",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-gray-600",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"当前筛选："}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-1",children:["all"!==tw&&(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center gap-1",children:["male"===tw?(0,n.jsx)(e9,{className:"w-3 h-3"}):"female"===tw?(0,n.jsx)(e4,{className:"w-3 h-3"}):(0,n.jsx)(e8,{className:"w-3 h-3"}),"male"===tw?"男生":"female"===tw?"女生":"中性"]}),"all"!==tk&&(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium flex items-center gap-1",children:["en"===tk?(0,n.jsx)(tr,{className:"w-3 h-3"}):"ja"===tk?(0,n.jsx)(tn,{className:"w-3 h-3"}):"es"===tk?(0,n.jsx)(to,{className:"w-3 h-3"}):"ko"===tk?(0,n.jsx)(ts,{className:"w-3 h-3"}):(0,n.jsx)(ta,{className:"w-3 h-3"}),"en"===tk?"英语":"ja"===tk?"日语":"es"===tk?"西班牙语":"ko"===tk?"韩语":"法语"]}),tf.trim()&&(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium flex items-center gap-1",children:[(0,n.jsx)(eu,{className:"w-3 h-3"}),'"',tf.length>10?tf.substring(0,10)+"...":tf,'"']}),tS&&(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs font-medium flex items-center gap-1",children:[(0,n.jsx)(eg,{className:"w-3 h-3 fill-current"}),"仅收藏"]})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-blue-600 font-medium",children:[rK.length," 个结果"]})]})}),(0,n.jsx)("div",{ref:rR,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:(0,n.jsx)(eW,{filteredVoices:rK,onSelectVoice:e=>{b(e),"dialogue"===rN&&null!==r$&&rX(r$,"voice",e),L(!1),rE(null)},currentVoiceId:p,previewingVoice:eY,handleVoicePreview:nh,voiceIconMapping:o,voiceIcons:m,listHeightClass:"max-h-80",favoriteVoiceIds:tC,onToggleFavorite:rx,showFavoriteButton:!0})})]})]})]})]}),(0,n.jsxs)(l.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-20",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsxs)(l.Wu,{className:"p-6 relative",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 mb-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl",children:(0,n.jsx)(ef,{className:"w-5 h-5 text-indigo-600"})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"模型"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative group",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative px-3 py-1.5 text-sm font-semibold bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 rounded-lg text-purple-700 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 cursor-default flex items-center gap-1",children:[(0,n.jsx)(ea.A,{className:"w-3 h-3"}),"只有Eleven v3才支持情感标注词"]})]})]})]}),(0,n.jsxs)("div",{ref:rP,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsxs)("button",{onClick:()=>D(!R),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ${R?"border-indigo-400 shadow-2xl shadow-indigo-100/50 ring-4 ring-indigo-50 scale-[1.02]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"}`,children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:I?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg flex items-center justify-center",children:rZ.find(e=>e.id===I)?.icon}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`font-semibold text-base ${"eleven_v3"===I?"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold":"text-gray-900"}`,children:rZ.find(e=>e.id===I)?.name})})]}):(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-500 text-base",children:"请选择模型..."})}),(0,n.jsx)(Q,{className:`w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-indigo-500 ${R?"rotate-180 text-indigo-500":""}`})]}),R&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" max-h-85 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",children:rZ.map(e=>(0,n.jsxs)("div",{onClick:()=>{M(e.id),D(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`relative p-3 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-indigo-50/50 group/item ${I===e.id?"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-100":""}`,children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-2xl transition-all duration-300 group-hover/item:scale-110 flex items-center justify-center",children:e.icon}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 mb-0.5",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`font-semibold text-base ${"eleven_v3"===e.id?"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold":"text-gray-900"}`,children:e.name}),I===e.id&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-3 h-3 bg-indigo-500 rounded-full animate-ping flex-shrink-0"})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),I===e.id&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-400 rounded-r animate-pulse"})]},e.id))})})]})]})]}),(0,n.jsxs)(l.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 overflow-hidden",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,n.jsxs)(l.Wu,{className:"p-6 relative",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 mb-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl",children:(0,n.jsx)(ex,{className:"w-5 h-5 text-purple-600"})}),(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"参数调整"})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-5",children:[{label:"稳定性",value:v,setter:k,color:"blue",max:1,min:0,step:.01},{label:"相似度",value:j,setter:N,color:"purple",max:1,min:0,step:.01},{label:"风格",value:z,setter:C,color:"green",max:1,min:0,step:.01},{label:"语速",value:A,setter:S,color:"orange",max:1.2,min:.7,step:.01}].filter(e=>"eleven_v3"===I?"稳定性"===e.label:"eleven_turbo_v2"!==I&&"eleven_turbo_v2_5"!==I||"风格"!==e.label).map((e,t)=>(0,n.jsxs)("div",{style:{animationDelay:`${100*t}ms`},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" group/param",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex justify-between items-center mb-3",children:[(0,n.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-semibold text-gray-700 group-hover/param:text-gray-900 transition-colors duration-300",children:e.label}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`text-sm font-mono px-2.5 py-1.5 rounded-lg font-bold transition-all duration-300 group-hover/param:scale-110 ${"blue"===e.color?"bg-blue-100 text-blue-800 group-hover/param:bg-blue-200":"purple"===e.color?"bg-purple-100 text-purple-800 group-hover/param:bg-purple-200":"green"===e.color?"bg-green-100 text-green-800 group-hover/param:bg-green-200":"bg-orange-100 text-orange-800 group-hover/param:bg-orange-200"}`,children:e.value[0].toFixed(2)})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)(q,{value:e.value,onValueChange:t=>{if("eleven_v3"===I&&"稳定性"===e.label){let r=t[0];e.setter([r<=.25?0:r<=.75?.5:1])}else e.setter(t)},max:e.max,min:e.min,step:"eleven_v3"===I&&"稳定性"===e.label?.5:e.step,className:"w-full group-hover/param:scale-105 transition-transform duration-300"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover/param:opacity-100 transition-opacity duration-500 pointer-events-none animate-shimmer"})]})]},e.label))})]})]}),(0,n.jsxs)(i.$,{onClick:()=>{if(rJ)return;f(!0),eR(null),tH("processing"),tW("正在建立安全连接..."),h(!1),G({streamUrl:null,downloadUrl:null,secureStreamUrl:null}),rF.current=!0,na();let e=no();tq(e),tJ.current=e,console.log("[TASK-MAPPING] Generated new logical task ID:",e),r7(e=>({...e,frontendAttempts:0,datacenterAttempts:0,backupApiAttempts:0,frontendInProgress:!1,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null,frontendStartTime:0,backupApiStartTime:0,errorHistory:[]})),tQ(0),t5([]),r3({input:d.trim(),voice:p,model:I,stability:v[0],similarity_boost:j[0],style:z[0],speed:A[0],taskType:"dialogue"===rN?"dialogue":"single",dialogueLines:"dialogue"===rN?rC:void 0});let t=eO.tC.getAccessToken();if(!t){eV(!0),f(!1);return}let r=new WebSocket(ni(rN));r.onopen=()=>{tW("连接成功，正在启动任务...");let e={action:"start",token:t,model:I};"single"===rN?(e.input=d.trim(),e.voice=p):"dialogue"===rN&&(e.taskType="dialogue",e.dialogue=rC.map(e=>({voice:e.voice,text:e.text.trim()}))),"eleven_v3"===I?e.stability=v[0]:(e.stability=v[0],e.similarity_boost=j[0],e.speed=A[0],"eleven_turbo_v2"!==I&&"eleven_turbo_v2_5"!==I&&(e.style=z[0])),r.send(JSON.stringify(e))},np(r)},disabled:rJ,className:`button-hover-optimized w-full text-lg lg:text-xl font-bold relative overflow-hidden group rounded-3xl transition-all duration-500 transform hover:scale-105 disabled:scale-100 shadow-2xl hover:shadow-3xl ${g?"h-16 sm:h-18 lg:h-20":"h-12 sm:h-14 lg:h-16"} ${rJ?"opacity-50 cursor-not-allowed":""}`,style:{background:"transparent"},children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 rounded-3xl overflow-hidden",children:(0,n.jsx)(eK,{className:"rounded-3xl"})}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 rounded-3xl"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10 text-white font-bold",children:g||t7.frontendInProgress?(0,n.jsx)(()=>(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"flex gap-1",children:[0,1,2].map(e=>(0,n.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:`${.15*e}s`}},e))}),(0,n.jsx)("span",{className:"animate-pulse",children:t7.frontendInProgress?`正在重试... (${t7.frontendAttempts}/${t7.frontendMaxAttempts})`:"processing"===tB?"生成中...":"处理中..."})]}),tK&&(0,n.jsx)("div",{className:"text-xs text-white/80 text-center max-w-xs",children:tK})]}),{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])}):(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 text-xl",children:"生成音频"})})]})]})]})]}),(0,n.jsx)(eT.lG,{open:eD,onOpenChange:eP,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,n.jsxs)(eT.c7,{className:"text-center space-y-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center",children:(0,n.jsx)(eh,{className:"w-8 h-8 text-orange-500"})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-6",children:[(0,n.jsx)(eT.L3,{className:"text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:"配额不足"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative px-3 py-1.5 rounded-full shadow-sm hover:scale-105 transition-all duration-300 overflow-hidden",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-pink-400 via-red-400 via-orange-400 via-yellow-400 via-green-400 via-teal-400 via-blue-400 via-indigo-400 via-purple-400 to-pink-400 animate-rainbow-bg rounded-full"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative bg-white/90 backdrop-blur-sm rounded-full px-3 py-1.5 m-0.5",children:(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"免费体验找客服微信sunshine-12-06"})})]})]}),(0,n.jsxs)(eT.rr,{className:"text-gray-600 space-y-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,n.jsx)(eh,{className:"w-5 h-5 text-orange-500 flex-shrink-0"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"您当前没有配音权限或配额已用完"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,n.jsx)(eo.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"请充值获取会员权限后继续使用"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,n.jsx)(ey.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"我们提供多种套餐选择，满足不同需求"})]})]})]}),(0,n.jsxs)(eT.Es,{className:"flex-col sm:flex-row gap-3 mt-6",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>eP(!1),className:"w-full sm:w-auto border-gray-300 hover:bg-gray-50",children:"稍后再说"}),(0,n.jsxs)(i.$,{onClick:()=>{eP(!1),window.location.href="/recharge"},className:"w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,n.jsx)(ew.A,{className:"w-4 h-4 mr-2"}),"立即充值"]})]})]})}),(0,n.jsx)(eT.lG,{open:e7,onOpenChange:tc,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,n.jsxs)(eT.c7,{className:"text-center space-y-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-8 h-8 text-purple-500",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})}),(0,n.jsxs)(eT.L3,{className:"text-2xl font-bold",children:["解锁 ",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent",children:"PRO"})," 功能"]}),(0,n.jsxs)(eT.rr,{className:"text-gray-600 space-y-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200",children:[(0,n.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-5 h-5 text-purple-500 flex-shrink-0",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"多人对话功能仅限 PRO 会员使用"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,n.jsx)(eo.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"升级即可享受更多高级功能"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,n.jsx)(ey.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"优先处理队列，更快生成速度"})]})]})]}),(0,n.jsxs)(eT.Es,{className:"flex-col sm:flex-row gap-3 mt-6",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>tc(!1),className:"w-full sm:w-auto border-gray-300 hover:bg-gray-50",children:"稍后再说"}),(0,n.jsxs)(i.$,{onClick:()=>{tc(!1),window.location.href="/recharge"},className:"w-full sm:w-auto bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,n.jsx)(ew.A,{className:"w-4 h-4 mr-2"}),"立即升级"]})]})]})}),(0,n.jsx)(eT.lG,{open:e_,onOpenChange:eV,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,n.jsxs)(eT.c7,{className:"text-center space-y-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,n.jsx)(ey.A,{className:"w-8 h-8 text-blue-500"})}),(0,n.jsx)(eT.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,n.jsx)(eT.rr,{className:"text-gray-600",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,n.jsx)(eT.Es,{className:"mt-6",children:(0,n.jsxs)(i.$,{onClick:async()=>{eV(!1),await eF.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,n.jsx)(en.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})}),(0,n.jsx)(s(),{id:"dc95a5d6ce23cfb7",dynamic:[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""],children:`@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-moz-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-o-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-webkit-keyframes rainbow-bg{0%{background-position:0%50%;-webkit-filter:hue-rotate(0deg)saturate(1.2)brightness(1.1);filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;-webkit-filter:hue-rotate(60deg)saturate(1.3)brightness(1.2);filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;-webkit-filter:hue-rotate(120deg)saturate(1.4)brightness(1.1);filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;-webkit-filter:hue-rotate(180deg)saturate(1.3)brightness(1.2);filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;-webkit-filter:hue-rotate(240deg)saturate(1.2)brightness(1.1);filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;-webkit-filter:hue-rotate(300deg)saturate(1.3)brightness(1.2);filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;-webkit-filter:hue-rotate(360deg)saturate(1.2)brightness(1.1);filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-moz-keyframes rainbow-bg{0%{background-position:0%50%;filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-o-keyframes rainbow-bg{0%{background-position:0%50%;filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@keyframes rainbow-bg{0%{background-position:0%50%;-webkit-filter:hue-rotate(0deg)saturate(1.2)brightness(1.1);filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;-webkit-filter:hue-rotate(60deg)saturate(1.3)brightness(1.2);filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;-webkit-filter:hue-rotate(120deg)saturate(1.4)brightness(1.1);filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;-webkit-filter:hue-rotate(180deg)saturate(1.3)brightness(1.2);filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;-webkit-filter:hue-rotate(240deg)saturate(1.2)brightness(1.1);filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;-webkit-filter:hue-rotate(300deg)saturate(1.3)brightness(1.2);filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;-webkit-filter:hue-rotate(360deg)saturate(1.2)brightness(1.1);filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-webkit-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-moz-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-o-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-webkit-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-moz-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-o-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-webkit-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-moz-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-o-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}@-webkit-keyframes shimmer{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);transform:translatex(100%)}}@-moz-keyframes shimmer{0%{-moz-transform:translatex(-100%);transform:translatex(-100%)}100%{-moz-transform:translatex(100%);transform:translatex(100%)}}@-o-keyframes shimmer{0%{-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-o-transform:translatex(100%);transform:translatex(100%)}}@keyframes shimmer{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%)}}@-webkit-keyframes breathe{0%,100%{opacity:.6;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}}@-moz-keyframes breathe{0%,100%{opacity:.6;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.05);transform:scale(1.05)}}@-o-keyframes breathe{0%,100%{opacity:.6;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.05);transform:scale(1.05)}}@keyframes breathe{0%,100%{opacity:.6;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}}@-webkit-keyframes breathe-glow{0%,100%{opacity:0;-webkit-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes breathe-glow{0%,100%{opacity:0;-moz-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes breathe-glow{0%,100%{opacity:0;-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes breathe-glow{0%,100%{opacity:0;-webkit-transform:scale(.8);-moz-transform:scale(.8);-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}${eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""}.__jsx-style-dynamic-selector .animate-float.__jsx-style-dynamic-selector{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-gradient.__jsx-style-dynamic-selector{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradient 3s ease infinite;-moz-animation:gradient 3s ease infinite;-o-animation:gradient 3s ease infinite;animation:gradient 3s ease infinite}.animate-fade-in.__jsx-style-dynamic-selector{-webkit-animation:fade-in.5s ease-out forwards;-moz-animation:fade-in.5s ease-out forwards;-o-animation:fade-in.5s ease-out forwards;animation:fade-in.5s ease-out forwards}.animate-shimmer.__jsx-style-dynamic-selector{-webkit-animation:shimmer 2s ease-in-out infinite;-moz-animation:shimmer 2s ease-in-out infinite;-o-animation:shimmer 2s ease-in-out infinite;animation:shimmer 2s ease-in-out infinite}.animate-breathe.__jsx-style-dynamic-selector{-webkit-animation:breathe 3s ease-in-out infinite;-moz-animation:breathe 3s ease-in-out infinite;-o-animation:breathe 3s ease-in-out infinite;animation:breathe 3s ease-in-out infinite}.animate-breathe-glow.__jsx-style-dynamic-selector{-webkit-animation:breathe-glow 4s ease-in-out infinite;-moz-animation:breathe-glow 4s ease-in-out infinite;-o-animation:breathe-glow 4s ease-in-out infinite;animation:breathe-glow 4s ease-in-out infinite}.scrollbar-thin.__jsx-style-dynamic-selector{scrollbar-width:thin}.scrollbar-thumb-gray-300.__jsx-style-dynamic-selector::-webkit-scrollbar-thumb{background-color:#d1d5db;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem}.scrollbar-track-gray-100.__jsx-style-dynamic-selector::-webkit-scrollbar-track{background-color:#f3f4f6}.scrollbar-thin.__jsx-style-dynamic-selector::-webkit-scrollbar{width:6px}@-webkit-keyframes spin-slow{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin-slow{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin-slow{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin-slow{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.animate-spin-slow.__jsx-style-dynamic-selector{-webkit-animation:spin-slow 8s linear infinite;-moz-animation:spin-slow 8s linear infinite;-o-animation:spin-slow 8s linear infinite;animation:spin-slow 8s linear infinite}.animate-rainbow-bg.__jsx-style-dynamic-selector{-webkit-background-size:600%100%;-moz-background-size:600%100%;-o-background-size:600%100%;background-size:600%100%;-webkit-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;-moz-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;-o-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite}`}),(0,n.jsx)(eT.lG,{open:t4,onOpenChange:t8,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200",children:[(0,n.jsxs)(eT.c7,{children:[(0,n.jsxs)(eT.L3,{className:"flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:[(0,n.jsx)(er,{className:"w-5 h-5 text-blue-600"}),"修改密码"]}),(0,n.jsx)(eT.rr,{className:"text-gray-600",children:"为了您的账户安全，请输入当前密码和新密码"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-4 py-4",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-2",children:[(0,n.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium text-gray-700",children:"当前密码"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)(Y.p,{type:rr?"text":"password",value:re.currentPassword,onChange:e=>rt(t=>({...t,currentPassword:e.target.value})),placeholder:"请输入当前密码",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,n.jsx)("button",{type:"button",onClick:()=>rn(!rr),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:rr?(0,n.jsx)(ev.A,{className:"w-4 h-4"}):(0,n.jsx)(ek.A,{className:"w-4 h-4"})})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-2",children:[(0,n.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium text-gray-700",children:"新密码"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)(Y.p,{type:ro?"text":"password",value:re.newPassword,onChange:e=>rt(t=>({...t,newPassword:e.target.value})),placeholder:"请输入新密码（至少6位）",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,n.jsx)("button",{type:"button",onClick:()=>rs(!ro),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:ro?(0,n.jsx)(ev.A,{className:"w-4 h-4"}):(0,n.jsx)(ek.A,{className:"w-4 h-4"})})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-2",children:[(0,n.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium text-gray-700",children:"确认新密码"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)(Y.p,{type:ra?"text":"password",value:re.confirmPassword,onChange:e=>rt(t=>({...t,confirmPassword:e.target.value})),placeholder:"请再次输入新密码",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,n.jsx)("button",{type:"button",onClick:()=>ri(!ra),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:ra?(0,n.jsx)(ev.A,{className:"w-4 h-4"}):(0,n.jsx)(ek.A,{className:"w-4 h-4"})})]})]}),rl&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-red-700",children:[(0,n.jsx)(eh,{className:"w-4 h-4"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium",children:rl})]})})]}),(0,n.jsxs)(eT.Es,{className:"flex gap-2",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>{t8(!1),rt({currentPassword:"",newPassword:"",confirmPassword:""}),rc("")},disabled:rm,className:"flex-1",children:"取消"}),(0,n.jsx)(i.$,{onClick:r2,disabled:rm,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white",children:rm?(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"修改中..."]}):"确认修改"})]})]})}),(0,n.jsx)(eT.lG,{open:ru,onOpenChange:rp,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200",children:[(0,n.jsxs)(eT.c7,{children:[(0,n.jsxs)(eT.L3,{className:"flex items-center gap-3 text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-md opacity-50"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full shadow-lg",children:(0,n.jsx)(ej.A,{className:"w-6 h-6 text-white"})})]}),"密码修改成功"]}),(0,n.jsx)(eT.rr,{className:"text-gray-600 text-center py-4 text-lg",children:"您的密码已成功更新，请妥善保管新密码"})]}),(0,n.jsx)(eT.Es,{className:"flex justify-center",children:(0,n.jsx)(i.$,{onClick:()=>rp(!1),className:"px-8 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:"确定"})})]})}),(0,n.jsx)(eT.lG,{open:rb,onOpenChange:rg,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-4xl max-h-[95vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-y-auto",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-br from-orange-50/80 via-red-50/60 to-yellow-50/80"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-3xl"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-200/30 to-orange-200/30 rounded-full blur-2xl"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-4 right-4 z-20 flex items-center gap-2",children:[(0,n.jsx)("button",{onClick:()=>{try{localStorage.setItem("hideContentPolicyNotice","true"),console.log("用户已选择永久隐藏内容政策通知。")}catch(e){console.error("无法在 localStorage 中保存设置:",e)}rg(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" group px-3 py-2 rounded-full text-sm font-medium bg-white/80 hover:bg-gray-100 text-gray-500 hover:text-red-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:"不再提示"}),(0,n.jsx)("button",{onClick:()=>rg(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300"}),(0,n.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-5 h-5 text-gray-600 group-hover:text-red-500 transition-colors duration-300",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M6 18L18 6M6 6l12 12",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})]})})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10",children:[(0,n.jsxs)(eT.c7,{className:"text-center space-y-4 pb-2",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-center gap-3 mb-2",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-orange-400 to-red-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 rounded-full shadow-xl",children:(0,n.jsx)(eh,{className:"w-8 h-8 text-white animate-bounce",style:{animationDuration:"2s"}})})]})}),(0,n.jsx)(eT.L3,{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-yellow-600 bg-clip-text text-transparent leading-tight text-center",children:"\uD83D\uDCE2 重要公告"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-2xl font-semibold text-center text-gray-800",children:"关于 ElevenLabs 内容审核机制"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-center gap-2 py-2",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-gradient-to-r from-orange-400 to-red-400 rounded-full animate-pulse"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1"})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3 py-1",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center",children:(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl text-gray-700 font-bold leading-relaxed font-medium",children:"尊敬的用户，您好！"})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-red-50/90 to-orange-50/90 rounded-3xl p-8 border-2 border-red-200/60 shadow-xl",children:[(0,n.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-2xl font-bold text-gray-800 mb-6 flex items-center justify-center gap-3",children:[(0,n.jsx)(eh,{className:"w-7 h-7 text-red-600"}),"内容政策提醒"]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center mb-6",children:(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg text-gray-700 leading-relaxed",children:["请注意 ElevenLabs 官方会对所有提交的文本内容进行",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-red-600",children:"自动审核"}),"。"]})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-red-200/60 shadow-lg",children:[(0,n.jsx)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2",children:"⚠️ 可能被拦截的内容"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" grid grid-cols-1 md:grid-cols-2 gap-4 text-left",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"暴力或威胁性内容"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"仇恨言论或歧视性内容"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"成人或性暗示内容"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"政治敏感内容"})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"版权保护的内容（如歌词、台词等）"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"可能用于欺诈的内容"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700",children:"其他违反 ElevenLabs 服务条款的内容"})]})]})]})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-blue-50/90 to-indigo-50/90 rounded-2xl p-6 border border-blue-200/60 shadow-lg max-w-4xl mx-auto",children:[(0,n.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-2xl",children:"\uD83D\uDCA1"}),"温馨提示"]}),(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700 leading-relaxed text-center max-w-3xl mx-auto text-lg",children:["为确保您的配音请求能够顺利处理，请在提交前检查文本内容是否符合 ElevenLabs 的",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-blue-700",children:"内容政策"}),"。如果您的内容被误判，可以尝试调整表达方式。"]})]}),(0,n.jsxs)("details",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" group",children:[(0,n.jsx)("summary",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" cursor-pointer list-none",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-300 group-open:rounded-b-none",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between",children:[(0,n.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl",children:"\uD83D\uDCCB"}),"审核机制详细说明"]}),(0,n.jsx)(Q,{className:"w-5 h-5 text-gray-500 group-open:rotate-180 transition-transform duration-300"})]}),(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-600 mt-2",children:"点击查看详细说明"})]})}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-white/60 backdrop-blur-sm rounded-b-2xl p-6 border border-t-0 border-gray-200/60 shadow-lg",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-6 max-w-4xl mx-auto",children:[(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700 leading-relaxed text-lg",children:["ElevenLabs 作为全球领先的AI语音合成平台，严格遵循",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-blue-600",children:"国际内容安全标准"}),"和",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-green-600",children:"法律法规要求"}),"，对所有用户提交的文本内容实施自动化审核机制。"]}),(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700 leading-relaxed text-lg",children:["该审核系统采用先进的AI技术，能够识别和拦截可能违反服务条款的内容。这一机制旨在",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-purple-600",children:"保护用户权益"}),"、",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-orange-600",children:"维护平台安全"}),"，并确保AI语音技术的负责任使用。"]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200/50",children:[(0,n.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg font-bold text-gray-800 mb-3 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl",children:"⚡"}),"如何避免内容被拦截？"]}),(0,n.jsxs)("ul",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-2 text-gray-700",children:[(0,n.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-start gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"使用积极正面的表达方式"})]}),(0,n.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-start gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"避免使用敏感词汇和争议性话题"})]}),(0,n.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-start gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"确保内容符合当地法律法规"})]}),(0,n.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-start gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"使用原创内容，避免版权争议"})]})]})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200/50",children:(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-700 leading-relaxed text-lg text-center",children:"我们理解内容审核可能会给您的创作带来一定限制，但这是为了确保平台的长期稳定运行和所有用户的安全体验。感谢您的理解与配合，让我们共同维护一个健康、安全的AI语音创作环境！"})})]})})]})]}),(0,n.jsx)(eT.Es,{className:"flex justify-center pt-4",children:(0,n.jsxs)(i.$,{onClick:()=>rg(!1),className:"px-8 py-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 hover:from-orange-600 hover:via-red-600 hover:to-yellow-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2",children:[(0,n.jsx)(ej.A,{className:"w-5 h-5"}),"我知道了"]})})]})]})}),(0,n.jsx)(eT.lG,{open:rh,onOpenChange:ry,children:(0,n.jsxs)(eT.Cf,{className:"sm:max-w-2xl bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-hidden",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-blue-50/80"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full blur-3xl"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200/30 to-indigo-200/30 rounded-full blur-2xl"}),(0,n.jsx)("button",{onClick:()=>ry(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-4 right-4 z-20 group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300"}),(0,n.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-5 h-5 text-gray-600 group-hover:text-purple-500 transition-colors duration-300",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M6 18L18 6M6 6l12 12",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])})})]})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative z-10",children:[(0,n.jsxs)(eT.c7,{className:"text-center space-y-4 pb-2",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-center gap-3 mb-2",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative p-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-full shadow-xl",children:(0,n.jsx)(ea.A,{className:"w-8 h-8 text-white animate-spin",style:{animationDuration:"3s"}})})]})}),(0,n.jsx)(eT.L3,{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent leading-tight text-center",children:"✨ 释放创造力：全新 v3 模型登场！"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-center gap-2 py-2",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1"})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-6 py-4",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center",children:(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg text-gray-700 leading-relaxed font-medium",children:["全新的 v3 模型不再是冰冷地朗读文字。它能理解您植入的",(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-purple-600 font-semibold",children:'"情感标签"'}),"，像一位专业配音演员，用最恰当的语调和情感来演绎您的文本。"]})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg",children:[(0,n.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-2xl",children:"\uD83C\uDFAD"}),"您可以尝试在文本中使用以下标签，探索各种可能性："]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-purple-700 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg",children:"\uD83D\uDE0A"}),"情绪类："]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex flex-wrap gap-2",children:["[愉快]","[悲伤]","[愤怒]","[激动]","[担忧]","[惊讶]"].map((e,t)=>(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm font-medium rounded-lg border border-purple-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},t))})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-blue-700 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg",children:"\uD83C\uDFA8"}),"风格类："]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex flex-wrap gap-2",children:["[旁白]","[耳语]","[轻声]","[严肃]","[广告语气]"].map((e,t)=>(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-medium rounded-lg border border-blue-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},t))})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-green-700 flex items-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg",children:"\uD83C\uDFAC"}),"动作类："]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex flex-wrap gap-2",children:["[笑声]","[叹气]"].map((e,t)=>(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-sm font-medium rounded-lg border border-green-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},t))})]})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-xl",children:(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center text-gray-700 font-medium flex items-center justify-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xl",children:"\uD83D\uDCA1"}),(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]]),children:"提示：您可以自由组合，创造出独一无二的声音效果！"})]})})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center",children:(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm text-gray-500 flex items-center justify-center gap-2",children:[(0,n.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),"此弹窗将在 5 秒后自动关闭"]})})]}),(0,n.jsx)(eT.Es,{className:"flex justify-center pt-4",children:(0,n.jsxs)(i.$,{onClick:()=>ry(!1),className:"px-8 py-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2",children:[(0,n.jsx)(ej.A,{className:"w-5 h-5"}),"我知道了"]})})]})]})}),(0,n.jsx)(eT.lG,{open:tN,onOpenChange:tz,children:(0,n.jsx)(eT.Cf,{className:"max-w-6xl max-h-[90vh] overflow-hidden p-0 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-3xl shadow-3xl",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex h-[80vh]",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-80 bg-gradient-to-br from-purple-50/80 to-pink-50/60 border-r border-gray-200/60 p-6 overflow-y-auto",children:[(0,n.jsxs)(eT.c7,{className:"mb-6",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl",children:(0,n.jsx)(eb,{className:"w-6 h-6 text-purple-600"})}),(0,n.jsx)(eT.L3,{className:"text-2xl font-bold text-gray-900",children:"筛选声音"})]}),(0,n.jsx)(eT.rr,{className:"text-gray-600 mt-2",children:"设置筛选条件，找到最适合的声音"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-6",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-1 bg-gradient-to-r from-blue-100 to-purple-100 rounded",children:(0,n.jsx)(eu,{className:"w-3 h-3 text-blue-600"})}),"搜索声音"]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative group",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,n.jsx)(eu,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10"}),(0,n.jsx)("input",{type:"text",placeholder:"搜索声音名称或描述...",value:tM,onChange:e=>tR(e.target.value),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" relative w-full pl-11 pr-4 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"}),tM&&(0,n.jsx)("button",{onClick:()=>tR(""),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200",children:(0,n.jsx)(ep.A,{className:"w-4 h-4"})})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-1 bg-gradient-to-r from-green-100 to-blue-100 rounded",children:(0,n.jsx)(es.A,{className:"w-3 h-3 text-green-600"})}),"性别筛选"]}),(0,n.jsx)(tl,{value:t$,onChange:e=>tE(e),options:[{value:"all",label:"全部声音",icon:(0,n.jsx)(es.A,{className:"w-4 h-4"})},{value:"male",label:"男生声音",icon:(0,n.jsx)(te,{className:"w-4 h-4"})},{value:"female",label:"女生声音",icon:(0,n.jsx)(tt,{className:"w-4 h-4"})}],className:"border-green-400 focus:ring-green-500/10",hoverColor:"green"})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-1 bg-gradient-to-r from-pink-100 to-red-100 rounded",children:(0,n.jsx)(eg,{className:"w-3 h-3 text-pink-600"})}),"我的收藏"]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3",children:[(0,n.jsxs)("button",{onClick:()=>tU(!t_),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-2xl border-2 transition-all duration-300 font-medium text-sm ${t_?"border-pink-400 bg-gradient-to-r from-pink-50 to-red-50 text-pink-600 shadow-lg":"border-gray-200 bg-white hover:border-pink-300 text-gray-600 hover:text-pink-500"}`,children:[(0,n.jsx)(eg,{className:`w-4 h-4 ${t_?"fill-current":""}`}),t_?"仅显示收藏":"显示全部"]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-lg",children:[tC.length," 个收藏"]})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3",children:[(0,n.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-1 bg-gradient-to-r from-orange-100 to-red-100 rounded",children:(0,n.jsx)(eN,{className:"w-3 h-3 text-orange-600"})}),"语言筛选"]}),(0,n.jsx)(tl,{value:tL,onChange:e=>tI(e),options:[{value:"all",label:"全部语言",icon:(0,n.jsx)(ti,{className:"w-4 h-4"})},{value:"en",label:"英语",icon:(0,n.jsx)(tr,{className:"w-4 h-4"})},{value:"ja",label:"日语",icon:(0,n.jsx)(tn,{className:"w-4 h-4"})},{value:"es",label:"西班牙语",icon:(0,n.jsx)(to,{className:"w-4 h-4"})},{value:"ko",label:"韩语",icon:(0,n.jsx)(ts,{className:"w-4 h-4"})},{value:"fr",label:"法语",icon:(0,n.jsx)(ta,{className:"w-4 h-4"})}],className:"border-orange-400 focus:ring-orange-500/10",hoverColor:"orange"})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" p-4 bg-gradient-to-r from-blue-50/80 to-purple-50/60 rounded-2xl border border-blue-200/50",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm text-gray-600 font-medium",children:"筛选结果"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg font-bold text-blue-600",children:[rW.length," 个声音"]})]})}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" space-y-3 pt-4 border-t border-gray-200/60",children:[(0,n.jsx)("button",{onClick:()=>{tE("all"),tI("all"),tR(""),tU(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-full px-4 py-3 text-sm text-red-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 rounded-2xl transition-all duration-200 font-medium",children:"重置筛选"}),(0,n.jsxs)("button",{onClick:rq,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-full px-4 py-3 text-sm text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-semibold flex items-center justify-center gap-2",children:[(0,n.jsx)(ej.A,{className:"w-4 h-4"}),"应用筛选 (",rW.length,")"]})]})]})]}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex-1 p-6 overflow-y-auto",children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" mb-4",children:[(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg font-semibold text-gray-900 mb-2",children:"筛选结果"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center justify-between",children:[(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm text-gray-600",children:["共找到 ",rW.length," 个符合条件的声音"]}),(0,n.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-xs text-violet-500 bg-gray-50 px-2 py-1 rounded-lg flex items-center gap-1",children:[(0,n.jsx)(ea.A,{className:"w-3 h-3"}),"双击声音卡片快速选择并应用"]})]})]}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:rW.map(e=>(0,n.jsxs)("div",{onClick:()=>{tP(e.id)},onDoubleClick:()=>{tP(e.id),rq()},className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`group relative p-4 border-2 rounded-2xl transition-all duration-300 cursor-pointer hover:shadow-lg ${tD===e.id?"border-blue-400 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg":"border-gray-200 bg-white hover:border-gray-300"}`,children:[(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-3 mb-3",children:[(0,n.jsx)("img",{src:o[e.id],alt:e.name,className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" w-10 h-10 rounded-full object-cover shadow-md"}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex-1 min-w-0",children:[(0,n.jsx)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" font-semibold text-gray-900 truncate",children:e.name}),(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-center gap-2 text-xs",children:[(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`px-2 py-1 rounded-full text-white font-medium flex items-center gap-1 ${"male"===e.gender?"bg-blue-500":"female"===e.gender?"bg-pink-500":"bg-gray-500"}`,children:["male"===e.gender?(0,n.jsx)(e9,{className:"w-3 h-3"}):"female"===e.gender?(0,n.jsx)(e4,{className:"w-3 h-3"}):(0,n.jsx)(e8,{className:"w-3 h-3"}),"male"===e.gender?"男":"female"===e.gender?"女":"中性"]}),(0,n.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" px-2 py-1 bg-gray-100 text-gray-700 rounded-full font-medium flex items-center gap-1",children:["en"===e.language?(0,n.jsx)(tr,{className:"w-3 h-3"}):"ja"===e.language?(0,n.jsx)(tn,{className:"w-3 h-3"}):"es"===e.language?(0,n.jsx)(to,{className:"w-3 h-3"}):"ko"===e.language?(0,n.jsx)(ts,{className:"w-3 h-3"}):"fr"===e.language?(0,n.jsx)(ta,{className:"w-3 h-3"}):(0,n.jsx)(ti,{className:"w-3 h-3"}),"en"===e.language?"英语":"ja"===e.language?"日语":"es"===e.language?"西班牙语":"ko"===e.language?"韩语":"fr"===e.language?"法语":"其他"]})]})]})]}),(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm text-gray-600 line-clamp-2",children:e.description}),(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),rx(e.id)},title:tC.includes(e.id)?"取消收藏":"添加收藏",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" "+`absolute top-3 right-3 p-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 ${tC.includes(e.id)?"bg-pink-100 hover:bg-pink-200":"bg-white/90 hover:bg-white"}`,children:(0,n.jsx)(eg,{className:`w-4 h-4 transition-colors duration-200 ${tC.includes(e.id)?"text-pink-600 fill-current":"text-gray-500 hover:text-pink-600"}`})}),e.preview&&(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),nh(e.preview,e.id)},title:eY===e.id?"停止预览":"预览声音",className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-3 right-14 p-2 bg-white/80 hover:bg-white rounded-full shadow-md hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100",children:eY===e.id?(0,n.jsx)(ec,{className:"w-4 h-4 text-blue-600"}):(0,n.jsx)(em,{className:"w-4 h-4 text-blue-600"})}),tD===e.id&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute top-3 left-3 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-md",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-75"})})]},e.id))}),0===rW.length&&(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-center py-12",children:[(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-400 mb-4",children:(0,n.jsx)(eu,{className:"w-16 h-16 mx-auto"})}),(0,n.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-lg font-semibold text-gray-600 mb-2",children:"没有找到匹配的声音"}),(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-gray-500",children:"请尝试调整筛选条件"})]})]})]})})}),tm&&(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm",children:(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" bg-orange-500 text-white px-6 py-4 rounded-lg shadow-xl max-w-md mx-4 animate-in zoom-in-95 duration-300",children:(0,n.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex items-start gap-3",children:[(0,n.jsx)(eh,{className:"w-5 h-5 mt-0.5 flex-shrink-0"}),(0,n.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" flex-1",children:(0,n.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-sm font-medium",children:tu})}),(0,n.jsx)("button",{onClick:()=>td(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[eE?`
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        `:""]]])+" text-white/80 hover:text-white transition-colors",children:(0,n.jsx)(ep.A,{className:"w-4 h-4"})})]})})})]})}},1312:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4042:(e,t,r)=>{Promise.resolve().then(r.bind(r,597))},5478:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,tu:()=>l,zf:()=>i});let n={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},o=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],s={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function a(e){if(e?.code)return Object.values(n).includes(e.code);if(e?.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function i(e){let t="";return(t=e?.message?e.message:e?.error?e.error:"string"==typeof e?e:"操作失败，请稍后重试",a(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return s.default;if(e&&"string"==typeof e&&o.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return s.network;if(t.includes("timeout"))return s.timeout;if(t.includes("auth")||t.includes("token"))return s.auth;else if(t.includes("server")||t.includes("internal"))return s.server;else if(t.includes("database"))return s.database;else return s.unknown}return e}(t)}function l(e,t){let r=a(e);r&&t&&t();let n=a(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:e?.shouldRedirect===void 0||e.shouldRedirect}:{title:"操作失败",description:i(e),shouldRedirect:!1};return{isAuthError:r,message:n.description,shouldRedirect:n.shouldRedirect}}function c(e){return a(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:e?.message?.includes("卡密")?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:i(e),variant:"destructive"}}},5583:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},5778:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6085:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6349:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7090:(e,t,r)=>{Promise.resolve().then(r.bind(r,850))},7826:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>d,Es:()=>p,L3:()=>b,c7:()=>u,lG:()=>l,rr:()=>g});var n=r(687),o=r(3210),s=r(8960),a=r(1860),i=r(6241);let l=s.bL;s.l9;let c=s.ZL;s.bm;let m=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hJ,{ref:r,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));m.displayName=s.hJ.displayName;let d=o.forwardRef(({className:e,children:t,...r},o)=>(0,n.jsxs)(c,{children:[(0,n.jsx)(m,{}),(0,n.jsxs)(s.UC,{ref:o,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(a.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));d.displayName=s.UC.displayName;let u=({className:e,...t})=>(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});u.displayName="DialogHeader";let p=({className:e,...t})=>(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="DialogFooter";let b=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hE,{ref:r,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));b.displayName=s.hE.displayName;let g=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.VY,{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=s.VY.displayName},8869:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},8988:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(687),o=r(3210),s=r(6241);let a=o.forwardRef(({className:e,type:t,...r},o)=>(0,n.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));a.displayName="Input"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[995,960,574,773],()=>r(275));module.exports=n})();