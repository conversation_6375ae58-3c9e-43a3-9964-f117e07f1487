(()=>{var e={};e.id=154,e.ids=[154],e.modules={83:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},103:(e,t,s)=>{Promise.resolve().then(s.bind(s,4057))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4057:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts-worker\\\\frontend\\\\app\\\\test-real-token-expiry\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts-worker\\frontend\\app\\test-real-token-expiry\\page.tsx","default")},5478:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,tu:()=>l,zf:()=>i});let r={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},a=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],n={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function o(e){if(e?.code)return Object.values(r).includes(e.code);if(e?.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function i(e){let t="";return(t=e?.message?e.message:e?.error?e.error:"string"==typeof e?e:"操作失败，请稍后重试",o(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return n.default;if(e&&"string"==typeof e&&a.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return n.network;if(t.includes("timeout"))return n.timeout;if(t.includes("auth")||t.includes("token"))return n.auth;else if(t.includes("server")||t.includes("internal"))return n.server;else if(t.includes("database"))return n.database;else return n.unknown}return e}(t)}function l(e,t){let s=o(e);s&&t&&t();let r=o(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:e?.shouldRedirect===void 0||e.shouldRedirect}:{title:"操作失败",description:i(e),shouldRedirect:!1};return{isAuthError:s,message:r.description,shouldRedirect:r.shouldRedirect}}function d(e){return o(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:e?.message?.includes("卡密")?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:i(e),variant:"destructive"}}},6349:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6443:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(687),a=s(3210),n=s(4934),o=s(5192),i=s(7826),l=s(4426),d=s(9556),c=s(1702),u=s(5478),p=s(6349),f=s(83);function m(){let[e,t]=(0,a.useState)(""),[s,m]=(0,a.useState)(!1),{toast:x}=(0,c.dj)(),g=()=>{let e=btoa(JSON.stringify({alg:"HS256",typ:"JWT"})),t=btoa(JSON.stringify({sub:"<EMAIL>",type:"access",iat:Date.now()-72e5,exp:Date.now()-36e5})),s=btoa("fake_signature_for_testing");return`${e}.${t}.${s}`},h=()=>{let e=btoa(JSON.stringify({alg:"HS256",typ:"JWT"})),t=btoa(JSON.stringify({sub:"<EMAIL>",type:"refresh",iat:Date.now()-72e5,exp:Date.now()-36e5})),s=btoa("fake_refresh_signature_for_testing");return`${e}.${t}.${s}`},y=async()=>{try{t("开始获取用户状态...");let e=await l.j2.getUserQuota();t("成功获取用户状态: "+JSON.stringify(e,null,2))}catch(r){console.error("获取用户状态失败:",r),t(`获取用户状态失败: ${r.message}
错误码: ${r.code||"无"}
shouldRedirect: ${r.shouldRedirect||"无"}`);let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(r,()=>{console.log("认证错误回调被触发，显示弹窗"),m(!0)});console.log("错误处理结果:",{isAuth:e,shouldRedirect:s}),e&&s&&(console.log("将在2秒后跳转到登录页面"),setTimeout(()=>{console.log("执行跳转到登录页面"),window.location.href="/login"},2e3))}};return(0,r.jsxs)("div",{className:"container mx-auto p-4",children:[(0,r.jsxs)(o.Zp,{className:"max-w-3xl mx-auto",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"真实Token过期测试"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"测试真正过期的JWT token vs 无效字符串的区别"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.$,{onClick:()=>{let e=d.tC.getAccessToken(),s=d.tC.getRefreshToken(),r="无",a="无";if(e)try{let t=e.split(".");if(3===t.length){let s=JSON.parse(atob(t[1])),a=Date.now()>s.exp;r=`${e.substring(0,20)}... (过期: ${a?"是":"否"}, 过期时间: ${new Date(s.exp).toLocaleString()})`}else r=`${e.substring(0,20)}... (格式: 无效)`}catch{r=`${e.substring(0,20)}... (格式: 无效)`}if(s)try{let e=s.split(".");if(3===e.length){let t=JSON.parse(atob(e[1])),r=Date.now()>t.exp;a=`${s.substring(0,20)}... (过期: ${r?"是":"否"}, 过期时间: ${new Date(t.exp).toLocaleString()})`}else a=`${s.substring(0,20)}... (格式: 无效)`}catch{a=`${s.substring(0,20)}... (格式: 无效)`}t(`当前tokens状态:
Access Token: ${r}
Refresh Token: ${a}`)},variant:"outline",children:"检查当前Tokens详情"}),(0,r.jsx)(n.$,{onClick:()=>{let e=g(),s=h();d.tC.setTokens(e,s,"<EMAIL>"),t(`已设置真正过期的JWT tokens:
Access Token (前50字符): ${e.substring(0,50)}...
Refresh Token (前50字符): ${s.substring(0,50)}...

现在可以测试fetchUserStatus，应该能正确识别为TOKEN_EXPIRED`)},variant:"secondary",children:"设置真正过期的JWT Tokens"}),(0,r.jsx)(n.$,{onClick:()=>{d.tC.setTokens("invalid_access_token","invalid_refresh_token","<EMAIL>"),t("已设置无效的tokens（对比测试），现在可以测试fetchUserStatus")},variant:"secondary",children:"设置无效字符串Tokens（对比）"}),(0,r.jsx)(n.$,{onClick:y,variant:"default",children:"测试fetchUserStatus"}),(0,r.jsx)(n.$,{onClick:()=>{d.tC.clearTokens(),t("已清除所有tokens"),m(!1)},variant:"destructive",children:"清除所有Tokens"})]}),e&&(0,r.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,r.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]})]})]}),(0,r.jsx)(i.lG,{open:s,onOpenChange:m,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,r.jsxs)(i.c7,{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-8 h-8 text-blue-500"})}),(0,r.jsx)(i.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,r.jsx)(i.rr,{className:"text-gray-600",children:(0,r.jsx)("div",{className:"flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,r.jsx)("span",{className:"text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,r.jsx)(i.Es,{className:"mt-6",children:(0,r.jsxs)(n.$,{onClick:async()=>{m(!1),await l.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})})]})}},6895:(e,t,s)=>{Promise.resolve().then(s.bind(s,6443))},7826:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>f,L3:()=>m,c7:()=>p,lG:()=>l,rr:()=>x});var r=s(687),a=s(3210),n=s(8960),o=s(1860),i=s(6241);let l=n.bL;n.l9;let d=n.ZL;n.bm;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=n.hJ.displayName;let u=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(d,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(n.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,r.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=n.UC.displayName;let p=({className:e,...t})=>(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let f=({className:e,...t})=>(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));m.displayName=n.hE.displayName;let x=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=n.VY.displayName},8855:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(5239),a=s(8088),n=s(8170),o=s.n(n),i=s(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["test-real-token-expiry",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4057)),"D:\\myaitts-worker\\frontend\\app\\test-real-token-expiry\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\myaitts-worker\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\myaitts-worker\\frontend\\app\\test-real-token-expiry\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-real-token-expiry/page",pathname:"/test-real-token-expiry",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[995,960,773],()=>s(8855));module.exports=r})();