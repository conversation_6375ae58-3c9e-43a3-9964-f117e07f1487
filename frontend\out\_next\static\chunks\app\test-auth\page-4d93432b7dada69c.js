(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[580],{3580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>h});var i=s(2115);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=a(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>u({type:"DISMISS_TOAST",toastId:s});return u({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||i()}}}),{id:s,dismiss:i,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function h(){let[e,t]=i.useState(d);return i.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},7492:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,tu:()=>l,zf:()=>a});let i={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},r=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],n={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function o(e){if(null==e?void 0:e.code)return Object.values(i).includes(e.code);if(null==e?void 0:e.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function a(e){let t="";return(t=(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error)?e.error:"string"==typeof e?e:"操作失败，请稍后重试",o(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return n.default;if(e&&"string"==typeof e&&r.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return n.network;if(t.includes("timeout"))return n.timeout;if(t.includes("auth")||t.includes("token"))return n.auth;else if(t.includes("server")||t.includes("internal"))return n.server;else if(t.includes("database"))return n.database;else return n.unknown}return e}(t)}function l(e,t){let s=o(e);s&&t&&t();let i=o(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:a(e),shouldRedirect:!1};return{isAuthError:s,message:i.description,shouldRedirect:i.shouldRedirect}}function d(e){var t;return o(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(t=e.message)||void 0===t?void 0:t.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:a(e),variant:"destructive"}}},9271:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var i=s(5155),r=s(2115),n=s(7168),o=s(8482),a=s(6194),l=s(1886),d=s(3580),u=s(7492);function c(){let[e,t]=(0,r.useState)(""),[s,c]=(0,r.useState)(!1),{toast:h}=(0,d.dj)(),p=async()=>{try{l.tC.setTokens("invalid_token","invalid_refresh_token","<EMAIL>");let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(i){console.error("测试错误:",i),t("捕获错误: "+i.message+" (code: "+(i.code||"无")+")");let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(i,()=>{c(!0),h({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&s&&setTimeout(()=>{window.location.href="/login"},2e3)}},T=async()=>{try{l.tC.clearTokens();let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(i){console.error("测试错误:",i),t("捕获错误: "+i.message+" (code: "+(i.code||"无")+")");let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(i,()=>{c(!0),h({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&s&&setTimeout(()=>{window.location.href="/login"},2e3)}};return(0,i.jsx)("div",{className:"container mx-auto p-4",children:(0,i.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,i.jsx)(o.aR,{children:(0,i.jsx)(o.ZB,{children:"认证错误处理测试"})}),(0,i.jsxs)(o.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.$,{onClick:p,variant:"outline",children:"测试无效Token"}),(0,i.jsx)(n.$,{onClick:T,variant:"outline",children:"测试无Token"})]}),e&&(0,i.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,i.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]}),s&&(0,i.jsxs)("div",{className:"p-4 bg-red-100 border border-red-300 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-red-800",children:"认证错误弹窗已触发!"}),(0,i.jsx)("p",{className:"text-red-600",children:"正在准备跳转到登录页面..."})]})]})]})})}},9536:(e,t,s)=>{Promise.resolve().then(s.bind(s,9271))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,576,441,684,358],()=>t(9536)),_N_E=e.O()}]);