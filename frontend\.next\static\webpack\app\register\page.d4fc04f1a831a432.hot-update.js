"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n// API配置和服务层\n// 兼容 Cloudflare Pages 部署\n// API配置\nconst API_CONFIG = {\n    // 生产环境使用你的 Cloudflare Worker 域名\n    BASE_URL: \"http://localhost:3001\" || 0,\n    // 备用API配置 - 支持多个备用API（逗号分隔）\n    BACKUP_URL: \"http://localhost:3001\" || 0,\n    BACKUP_URLS:  true ? \"http://localhost:3001\".split(',').map((url)=>url.trim()).filter((url)=>url.length > 0) : 0,\n    ENABLE_BACKUP: \"false\" === 'true',\n    TIMEOUT: 30000\n};\n// API端点定义\nconst API_ENDPOINTS = {\n    // 认证相关\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        REGISTER: '/api/auth/register',\n        REFRESH: '/api/auth/refresh',\n        SEND_VERIFICATION: '/api/auth/send-verification',\n        VERIFY_EMAIL: '/api/auth/verify-email',\n        CHANGE_PASSWORD: '/api/auth/change-password',\n        FORGOT_PASSWORD: '/api/auth/forgot-password',\n        RESET_PASSWORD: '/api/auth/reset-password'\n    },\n    // TTS相关\n    TTS: {\n        GENERATE: '/api/tts/generate',\n        STATUS: '/api/tts/status',\n        STREAM: '/api/tts/stream',\n        DOWNLOAD: '/api/tts/download'\n    },\n    // 用户相关\n    USER: {\n        QUOTA: '/api/user/quota'\n    },\n    // 卡密相关\n    CARD: {\n        USE: '/api/card/use'\n    },\n    // 自动标注相关\n    AUTO_TAG: {\n        PROCESS: '/api/auto-tag/process',\n        STATUS: '/api/auto-tag/status',\n        ADMIN_STATS: '/api/auto-tag/admin/stats'\n    }\n};\n// Token管理\nclass TokenManager {\n    static getAccessToken() {\n        if (false) {}\n        return localStorage.getItem(this.ACCESS_TOKEN_KEY);\n    }\n    static getRefreshToken() {\n        if (false) {}\n        return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n    }\n    static getUserEmail() {\n        if (false) {}\n        return localStorage.getItem(this.USER_EMAIL_KEY);\n    }\n    static setTokens(accessToken, refreshToken, email) {\n        if (false) {}\n        localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);\n        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);\n        if (email) {\n            localStorage.setItem(this.USER_EMAIL_KEY, email);\n        }\n        // 保持兼容性\n        localStorage.setItem('isLoggedIn', 'true');\n    }\n    static clearTokens() {\n        if (false) {}\n        localStorage.removeItem(this.ACCESS_TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n        localStorage.removeItem(this.USER_EMAIL_KEY);\n        // 保持兼容性\n        localStorage.removeItem('isLoggedIn');\n    }\n    static isLoggedIn() {\n        return !!this.getAccessToken();\n    }\n}\nTokenManager.ACCESS_TOKEN_KEY = 'access_token';\nTokenManager.REFRESH_TOKEN_KEY = 'refresh_token';\nTokenManager.USER_EMAIL_KEY = 'userEmail';\n// HTTP客户端类\nclass ApiClient {\n    // 获取当前可用的API URL（主要用于外部访问）\n    getCurrentApiUrl() {\n        let useBackup = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, backupIndex = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (useBackup && this.enableBackup) {\n            // 优先使用多个备用API配置\n            if (this.backupURLs.length > 0 && backupIndex >= 0 && backupIndex < this.backupURLs.length) {\n                return this.backupURLs[backupIndex];\n            }\n            // 向后兼容：使用单个备用API配置\n            if (this.backupURL) {\n                return this.backupURL;\n            }\n        }\n        return this.baseURL;\n    }\n    // 检查备用API是否可用\n    isBackupApiAvailable() {\n        return this.enableBackup && (this.backupURLs.length > 0 || !!this.backupURL);\n    }\n    // 获取备用API数量\n    getBackupApiCount() {\n        if (!this.enableBackup) return 0;\n        return this.backupURLs.length > 0 ? this.backupURLs.length : this.backupURL ? 1 : 0;\n    }\n    // 获取指定索引的备用API URL\n    getBackupApiUrl(index) {\n        if (!this.enableBackup) return null;\n        // 优先使用多个备用API配置\n        if (this.backupURLs.length > 0) {\n            return index >= 0 && index < this.backupURLs.length ? this.backupURLs[index] : null;\n        }\n        // 向后兼容：使用单个备用API配置\n        return index === 0 && this.backupURL ? this.backupURL : null;\n    }\n    // 创建请求头\n    createHeaders() {\n        let includeAuth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        if (includeAuth) {\n            const token = TokenManager.getAccessToken();\n            if (token) {\n                headers['Authorization'] = \"Bearer \".concat(token);\n            }\n        }\n        return headers;\n    }\n    // 处理响应\n    async handleResponse(response) {\n        if (!response.ok) {\n            let errorMessage = \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            let errorCode = null;\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.error || errorData.message || errorMessage;\n                errorCode = errorData.code || null // 【新增】提取错误码\n                ;\n            } catch (e) {\n            // 如果无法解析JSON，使用默认错误消息\n            }\n            // 【新增】创建带有错误码的自定义错误对象\n            const error = new Error(errorMessage);\n            if (errorCode) {\n                error.code = errorCode;\n            }\n            throw error;\n        }\n        return await response.json();\n    }\n    // 通用请求方法\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const headers = this.createHeaders(includeAuth);\n        const config = {\n            ...options,\n            headers: {\n                ...headers,\n                ...options.headers\n            }\n        };\n        // 创建AbortController用于超时控制\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url, {\n                ...config,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return await this.handleResponse(response);\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    throw new Error('请求超时，请检查网络连接');\n                }\n                throw error;\n            }\n            throw new Error('网络请求失败');\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        let includeAuth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        return this.request(endpoint, {\n            method: 'GET'\n        }, includeAuth);\n    }\n    // POST请求\n    async post(endpoint, data) {\n        let includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        let includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        let includeAuth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        return this.request(endpoint, {\n            method: 'DELETE'\n        }, includeAuth);\n    }\n    constructor(){\n        this.baseURL = API_CONFIG.BASE_URL;\n        this.backupURL = API_CONFIG.BACKUP_URL;\n        this.backupURLs = API_CONFIG.BACKUP_URLS;\n        this.enableBackup = API_CONFIG.ENABLE_BACKUP;\n        this.timeout = API_CONFIG.TIMEOUT;\n    }\n}\n// 创建全局API客户端实例\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});