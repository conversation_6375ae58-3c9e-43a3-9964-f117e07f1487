(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[604],{2769:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(5155),n=s(2115),r=s(7168),o=s(8482),l=s(9840),i=s(6194),d=s(1886),c=s(3580),u=s(7492),f=s(4186),m=s(4835);function p(){let[e,t]=(0,n.useState)(""),[s,p]=(0,n.useState)(!1),{toast:h}=(0,c.dj)(),g=async()=>{try{t("开始获取用户状态...");let e=await i.j2.getUserQuota();t("成功获取用户状态: "+JSON.stringify(e,null,2))}catch(a){console.error("获取用户状态失败:",a),t("获取用户状态失败: ".concat(a.message,"\n错误码: ").concat(a.code||"无","\nshouldRedirect: ").concat(a.shouldRedirect||"无"));let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(a,()=>{console.log("认证错误回调被触发，显示弹窗"),p(!0)});console.log("错误处理结果:",{isAuth:e,shouldRedirect:s}),e&&s&&(console.log("将在2秒后跳转到登录页面"),setTimeout(()=>{console.log("执行跳转到登录页面"),window.location.href="/login"},2e3))}};return(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{children:"主页面认证错误处理测试"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"模拟主页面的fetchUserStatus函数行为"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(r.$,{onClick:()=>{let e=d.tC.getAccessToken(),s=d.tC.getRefreshToken();t("当前tokens状态:\nAccess Token: ".concat(e?"".concat(e.substring(0,20),"..."):"无","\nRefresh Token: ").concat(s?"".concat(s.substring(0,20),"..."):"无"))},variant:"outline",children:"检查当前Tokens"}),(0,a.jsx)(r.$,{onClick:()=>{d.tC.setTokens("expired_access_token","expired_refresh_token","<EMAIL>"),t("已设置过期的tokens，现在可以测试fetchUserStatus")},variant:"secondary",children:"设置过期Tokens"}),(0,a.jsx)(r.$,{onClick:g,variant:"default",children:"测试fetchUserStatus (主页面逻辑)"}),(0,a.jsx)(r.$,{onClick:()=>{d.tC.clearTokens(),t("已清除所有tokens"),p(!1)},variant:"destructive",children:"清除所有Tokens"})]}),e&&(0,a.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,a.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]})]})]}),(0,a.jsx)(l.lG,{open:s,onOpenChange:p,children:(0,a.jsxs)(l.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,a.jsxs)(l.c7,{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"w-8 h-8 text-blue-500"})}),(0,a.jsx)(l.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,a.jsx)(l.rr,{className:"text-gray-600",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,a.jsx)("span",{className:"text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,a.jsx)(l.Es,{className:"mt-6",children:(0,a.jsxs)(r.$,{onClick:async()=>{p(!1),await i.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})})]})}},3082:(e,t,s)=>{Promise.resolve().then(s.bind(s,2769))},3580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f});var a=s(2115);let n=0,r=new Map,o=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],d={toasts:[]};function c(e){d=l(d,e),i.forEach(e=>{e(d)})}function u(e){let{...t}=e,s=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=a.useState(d);return a.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4835:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7492:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,tu:()=>i,zf:()=>l});let a={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},n=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],r={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function o(e){if(null==e?void 0:e.code)return Object.values(a).includes(e.code);if(null==e?void 0:e.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function l(e){let t="";return(t=(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error)?e.error:"string"==typeof e?e:"操作失败，请稍后重试",o(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return r.default;if(e&&"string"==typeof e&&n.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return r.network;if(t.includes("timeout"))return r.timeout;if(t.includes("auth")||t.includes("token"))return r.auth;else if(t.includes("server")||t.includes("internal"))return r.server;else if(t.includes("database"))return r.database;else return r.unknown}return e}(t)}function i(e,t){let s=o(e);s&&t&&t();let a=o(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:l(e),shouldRedirect:!1};return{isAuthError:s,message:a.description,shouldRedirect:a.shouldRedirect}}function d(e){var t;return o(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(t=e.message)||void 0===t?void 0:t.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:l(e),variant:"destructive"}}},9840:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>m,L3:()=>p,c7:()=>f,lG:()=>i,rr:()=>h});var a=s(5155),n=s(2115),r=s(3651),o=s(4416),l=s(3999);let i=r.bL;r.l9;let d=r.ZL;r.bm;let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n})});c.displayName=r.hJ.displayName;let u=n.forwardRef((e,t)=>{let{className:s,children:n,...i}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(r.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...i,children:[n,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};f.displayName="DialogHeader";let m=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};m.displayName="DialogFooter";let p=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",s),...n})});p.displayName=r.hE.displayName;let h=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...n})});h.displayName=r.VY.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[352,150,651,576,441,684,358],()=>t(3082)),_N_E=e.O()}]);