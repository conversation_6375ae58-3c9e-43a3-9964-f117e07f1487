/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/register/page";
exports.ids = ["app/register/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"07322ec7e04d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxteWFpdHRzXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA3MzIyZWM3ZTA0ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n\n\n\nconst metadata = {\n    title: 'AI Voice Generator',\n    description: 'Created with v0',\n    generator: 'v0.dev',\n    icons: {\n        icon: 'https://img.icons8.com/color/48/audiomack.png'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3NCO0FBQzJCO0FBRTFDLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsV0FBVztJQUNYQyxPQUFPO1FBQ0xDLE1BQU07SUFDUjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7O2dCQUNFSDs4QkFDRCw4REFBQ1IsMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWhCIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdG9hc3RlcidcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSBWb2ljZSBHZW5lcmF0b3InLFxuICBkZXNjcmlwdGlvbjogJ0NyZWF0ZWQgd2l0aCB2MCcsXG4gIGdlbmVyYXRvcjogJ3YwLmRldicsXG4gIGljb25zOiB7XG4gICAgaWNvbjogJ2h0dHBzOi8vaW1nLmljb25zOC5jb20vY29sb3IvNDgvYXVkaW9tYWNrLnBuZycsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDxUb2FzdGVyIC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdlbmVyYXRvciIsImljb25zIiwiaWNvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/register/page.tsx":
/*!*******************************!*\
  !*** ./app/register/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\myaitts\\frontend\\app\\register\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\myaitts\\frontend\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=D%3A%5Cmyaitts%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmyaitts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=D%3A%5Cmyaitts%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmyaitts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/register/page.tsx */ \"(rsc)/./app/register/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\myaitts\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/register/page\",\n        pathname: \"/register\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=D%3A%5Cmyaitts%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmyaitts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(rsc)/./components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteWFpdHRzJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbXlhaXR0cyU1QyU1Q2Zyb250ZW5kJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDdG9hc3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJEOlxcXFxteWFpdHRzXFxcXGZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcdG9hc3Rlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/register/page.tsx */ \"(rsc)/./app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteWFpdHRzJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNyZWdpc3RlciU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBbUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15YWl0dHNcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHJlZ2lzdGVyXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/register/page.tsx":
/*!*******************************!*\
  !*** ./app/register/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(ssr)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,Mic,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _hooks_use_email_verification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-email-verification */ \"(ssr)/./hooks/use-email-verification.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        username: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        agreeToTerms: false,\n        subscribeNewsletter: false\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [registrationError, setRegistrationError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isPageLoaded, setIsPageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [passwordStrength, setPasswordStrength] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 邮箱验证相关状态\n    const [verificationCode, setVerificationCode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showVerificationStep, setShowVerificationStep] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 使用邮箱验证Hook\n    const emailVerification = (0,_hooks_use_email_verification__WEBPACK_IMPORTED_MODULE_7__.useEmailVerification)({\n        onSuccess: {\n            \"RegisterPage.useEmailVerification[emailVerification]\": ()=>{\n                setShowSuccess(true);\n                // 2秒后跳转到主页\n                setTimeout({\n                    \"RegisterPage.useEmailVerification[emailVerification]\": ()=>{\n                        window.location.href = \"/\";\n                    }\n                }[\"RegisterPage.useEmailVerification[emailVerification]\"], 2000);\n            }\n        }[\"RegisterPage.useEmailVerification[emailVerification]\"],\n        onError: {\n            \"RegisterPage.useEmailVerification[emailVerification]\": (error)=>{\n                setRegistrationError(error);\n            }\n        }[\"RegisterPage.useEmailVerification[emailVerification]\"]\n    });\n    // 客户端挂载状态管理 - 解决水合失败问题\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            setIsPageLoaded(true);\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            // Calculate password strength\n            const calculatePasswordStrength = {\n                \"RegisterPage.useEffect.calculatePasswordStrength\": (password)=>{\n                    let strength = 0;\n                    if (password.length >= 8) strength += 1;\n                    if (/[a-z]/.test(password)) strength += 1;\n                    if (/[A-Z]/.test(password)) strength += 1;\n                    if (/[0-9]/.test(password)) strength += 1;\n                    if (/[^A-Za-z0-9]/.test(password)) strength += 1;\n                    return strength;\n                }\n            }[\"RegisterPage.useEffect.calculatePasswordStrength\"];\n            setPasswordStrength(calculatePasswordStrength(formData.password));\n        }\n    }[\"RegisterPage.useEffect\"], [\n        formData.password\n    ]);\n    const validateEmail = (email)=>{\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(email);\n    };\n    const validateUsername = (username)=>{\n        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;\n        return usernameRegex.test(username);\n    };\n    const validatePassword = (password)=>{\n        return password.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(password);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Username validation\n        if (!formData.username.trim()) {\n            newErrors.username = \"用户名不能为空\";\n        } else if (!validateUsername(formData.username)) {\n            newErrors.username = \"用户名只能包含字母、数字和下划线，长度3-20位\";\n        }\n        // Email validation\n        if (!formData.email.trim()) {\n            newErrors.email = \"邮箱地址不能为空\";\n        } else if (!validateEmail(formData.email)) {\n            newErrors.email = \"请输入有效的邮箱地址\";\n        }\n        // Password validation\n        if (!formData.password) {\n            newErrors.password = \"密码不能为空\";\n        } else if (!validatePassword(formData.password)) {\n            newErrors.password = \"密码至少8位，包含大小写字母和数字\";\n        }\n        // Confirm password validation\n        if (!formData.confirmPassword) {\n            newErrors.confirmPassword = \"请确认密码\";\n        } else if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"两次输入的密码不一致\";\n        }\n        // Terms agreement validation\n        if (!formData.agreeToTerms) {\n            newErrors.agreeToTerms = \"请同意服务条款和隐私政策\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear specific field error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // Clear registration error when user modifies form\n        if (registrationError) {\n            setRegistrationError(\"\");\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        // 如果还没有发送验证码，先发送验证码\n        if (!showVerificationStep) {\n            setIsLoading(true);\n            setRegistrationError(\"\");\n            try {\n                await emailVerification.sendVerificationCode({\n                    email: formData.email,\n                    username: formData.username,\n                    password: formData.password\n                });\n                // 切换到验证码输入步骤\n                setShowVerificationStep(true);\n            } catch (error) {\n            // 错误已经在Hook中处理\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            // 验证邮箱并完成注册\n            if (!verificationCode.trim()) {\n                setRegistrationError(\"请输入验证码\");\n                return;\n            }\n            setIsLoading(true);\n            setRegistrationError(\"\");\n            try {\n                await emailVerification.verifyEmailAndRegister(verificationCode);\n            // 成功处理在Hook的onSuccess回调中\n            } catch (error) {\n            // 错误已经在Hook中处理\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    };\n    // 返回到第一步\n    const handleBackToFirstStep = ()=>{\n        setShowVerificationStep(false);\n        setVerificationCode(\"\");\n        emailVerification.reset();\n        setRegistrationError(\"\");\n    };\n    const getPasswordStrengthColor = (strength)=>{\n        if (strength <= 1) return \"bg-red-500\";\n        if (strength <= 2) return \"bg-orange-500\";\n        if (strength <= 3) return \"bg-yellow-500\";\n        if (strength <= 4) return \"bg-blue-500\";\n        return \"bg-green-500\";\n    };\n    const getPasswordStrengthText = (strength)=>{\n        if (strength <= 1) return \"弱\";\n        if (strength <= 2) return \"一般\";\n        if (strength <= 3) return \"中等\";\n        if (strength <= 4) return \"强\";\n        return \"很强\";\n    };\n    // 预定义的粒子位置和动画参数 - 解决水合失败问题\n    const registerParticleConfigs = [\n        {\n            left: 18,\n            top: 22,\n            duration: 10\n        },\n        {\n            left: 72,\n            top: 38,\n            duration: 12\n        },\n        {\n            left: 42,\n            top: 62,\n            duration: 9\n        },\n        {\n            left: 82,\n            top: 16,\n            duration: 11\n        },\n        {\n            left: 28,\n            top: 78,\n            duration: 8\n        },\n        {\n            left: 62,\n            top: 48,\n            duration: 13\n        },\n        {\n            left: 52,\n            top: 32,\n            duration: 10\n        },\n        {\n            left: 38,\n            top: 82,\n            duration: 12\n        }\n    ];\n    const FloatingParticles = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n            children: registerParticleConfigs.map((config, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-float\",\n                    style: {\n                        left: `${config.left}%`,\n                        top: `${config.top}%`,\n                        animationDelay: `${i * 2}s`,\n                        animationDuration: `${config.duration}s`\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 238,\n            columnNumber: 5\n        }, this);\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden\",\n            children: [\n                isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingParticles, {}, void 0, false, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 22\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-10 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-full blur-lg opacity-50 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4\",\n                                    children: \"注册成功！\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg mb-6\",\n                                    children: \"欢迎加入 AI 语音工作室，正在为您跳转到主页面...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden\",\n        children: [\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingParticles, {\n                className: \"jsx-3e1eea4381e0e46b\"\n            }, void 0, false, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 20\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-green-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse\"\n            }, void 0, false, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    animationDelay: \"2s\"\n                },\n                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse\"\n            }, void 0, false, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3e1eea4381e0e46b\" + \" \" + `w-full max-w-2xl transition-all duration-1000 ${isPageLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-8\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"text-center pb-8 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex justify-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-lg opacity-50 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-8 h-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-blue-800 bg-clip-text text-transparent mb-2\",\n                                        children: \"创建账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-gray-600 text-lg\",\n                                        children: \"加入 AI 语音工作室，开启您的创作之旅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-6\",\n                                        children: [\n                                            (registrationError || emailVerification.hasError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-700 text-sm\",\n                                                        children: registrationError || emailVerification.sendError || emailVerification.verifyError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            emailVerification.isCodeSent && !emailVerification.hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3 animate-fade-in\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-green-700 text-sm\",\n                                                        children: [\n                                                            \"验证码已发送到 \",\n                                                            emailVerification.pendingRegistration?.email,\n                                                            \"，请查收邮件\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"username\",\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"block text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    \"用户名 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"username\",\n                                                                        type: \"text\",\n                                                                        value: formData.username,\n                                                                        onChange: (e)=>handleInputChange(\"username\", e.target.value),\n                                                                        placeholder: \"请输入用户名\",\n                                                                        className: `pl-10 h-12 text-lg border-2 transition-all duration-300 ${errors.username ? \"border-red-400 focus:border-red-500 focus:ring-red-100\" : \"border-gray-200 focus:border-green-400 focus:ring-green-100\"}`,\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500 text-sm flex items-center gap-2 animate-fade-in\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    errors.username\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"email\",\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"block text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    \"邮箱地址 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 26\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"email\",\n                                                                        type: \"email\",\n                                                                        value: formData.email,\n                                                                        onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                                        placeholder: \"请输入邮箱地址\",\n                                                                        className: `pl-10 h-12 text-lg border-2 transition-all duration-300 ${errors.email ? \"border-red-400 focus:border-red-500 focus:ring-red-100\" : \"border-gray-200 focus:border-green-400 focus:ring-green-100\"}`,\n                                                                        disabled: isLoading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500 text-sm flex items-center gap-2 animate-fade-in\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    errors.email\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"password\",\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"block text-sm font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"密码 \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 22\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"password\",\n                                                                type: showPassword ? \"text\" : \"password\",\n                                                                value: formData.password,\n                                                                onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                                                placeholder: \"请输入密码\",\n                                                                className: `pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${errors.password ? \"border-red-400 focus:border-red-500 focus:ring-red-100\" : \"border-gray-200 focus:border-green-400 focus:ring-green-100\"}`,\n                                                                disabled: isLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                disabled: isLoading,\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200\",\n                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex-1 bg-gray-200 rounded-full h-2 overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            width: `${passwordStrength / 5 * 100}%`\n                                                                        },\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + `h-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength)}`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                                    children: getPasswordStrengthText(passwordStrength)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500 text-sm flex items-center gap-2 animate-fade-in\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.password\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"confirmPassword\",\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"block text-sm font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"确认密码 \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 24\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"confirmPassword\",\n                                                                type: showConfirmPassword ? \"text\" : \"password\",\n                                                                value: formData.confirmPassword,\n                                                                onChange: (e)=>handleInputChange(\"confirmPassword\", e.target.value),\n                                                                placeholder: \"请再次输入密码\",\n                                                                className: `pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${errors.confirmPassword ? \"border-red-400 focus:border-red-500 focus:ring-red-100\" : \"border-gray-200 focus:border-green-400 focus:ring-green-100\"}`,\n                                                                disabled: isLoading\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                disabled: isLoading,\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200\",\n                                                                children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500 text-sm flex items-center gap-2 animate-fade-in\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.confirmPassword\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this),\n                                            showVerificationStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"verificationCode\",\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"block text-sm font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"邮箱验证码 \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"verificationCode\",\n                                                                type: \"text\",\n                                                                value: verificationCode,\n                                                                onChange: (e)=>setVerificationCode(e.target.value),\n                                                                placeholder: \"请输入6位验证码\",\n                                                                className: \"pl-10 h-12 text-lg border-2 transition-all duration-300 border-gray-200 focus:border-green-400 focus:ring-green-100\",\n                                                                disabled: isLoading,\n                                                                maxLength: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"ghost\",\n                                                                onClick: handleBackToFirstStep,\n                                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                                disabled: isLoading,\n                                                                children: \"← 返回修改信息\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                onClick: emailVerification.resendVerificationCode,\n                                                                disabled: !emailVerification.canResend || emailVerification.isSending,\n                                                                className: \"text-sm\",\n                                                                children: emailVerification.isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"发送中...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 25\n                                                                }, this) : emailVerification.getCountdownText()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this),\n                                            !showVerificationStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                                id: \"agreeToTerms\",\n                                                                checked: formData.agreeToTerms,\n                                                                onCheckedChange: (checked)=>handleInputChange(\"agreeToTerms\", checked),\n                                                                disabled: isLoading,\n                                                                className: \"data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"agreeToTerms\",\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-sm text-gray-700 cursor-pointer select-none leading-relaxed\",\n                                                                        children: [\n                                                                            \"我已阅读并同意\",\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>alert(\"服务条款页面\"),\n                                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-green-600 hover:text-green-800 hover:underline font-semibold\",\n                                                                                children: \"服务条款\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" \",\n                                                                            \"和\",\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>alert(\"隐私政策页面\"),\n                                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-green-600 hover:text-green-800 hover:underline font-semibold\",\n                                                                                children: \"隐私政策\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                                lineNumber: 591,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    errors.agreeToTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-red-500 text-sm flex items-center gap-2 mt-2 animate-fade-in\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            errors.agreeToTerms\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                                id: \"subscribeNewsletter\",\n                                                                checked: formData.subscribeNewsletter,\n                                                                onCheckedChange: (checked)=>handleInputChange(\"subscribeNewsletter\", checked),\n                                                                disabled: isLoading,\n                                                                className: \"data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"subscribeNewsletter\",\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-sm text-gray-700 cursor-pointer select-none\",\n                                                                children: \"订阅产品更新和优惠信息\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                type: \"submit\",\n                                                disabled: isLoading || showVerificationStep && !verificationCode.trim(),\n                                                className: \"w-full h-12 text-lg font-bold bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 hover:from-green-600 hover:via-blue-600 hover:to-purple-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"relative z-10 flex items-center justify-center gap-3\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                showVerificationStep ? '验证中...' : '发送验证码中...'\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: showVerificationStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"完成注册\"\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"发送验证码\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_Mic_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-sm text-blue-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"font-semibold mb-1\",\n                                                                children: \"安全提示\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3e1eea4381e0e46b\",\n                                                                children: \"您的个人信息将被安全加密存储，我们承诺不会向第三方泄露您的隐私信息。\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"mt-8 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-gray-600\",\n                                            children: [\n                                                \"已有账户？\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>window.location.href = \"/login\",\n                                                    className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-green-600 hover:text-green-800 font-semibold hover:underline transition-colors duration-200\",\n                                                    children: \"立即登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"jsx-3e1eea4381e0e46b\" + \" \" + \"text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200\",\n                            children: \"← 返回首页\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3e1eea4381e0e46b\",\n                children: \"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-float.jsx-3e1eea4381e0e46b{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-fade-in.jsx-3e1eea4381e0e46b{-webkit-animation:fade-in.3s ease-out forwards;-moz-animation:fade-in.3s ease-out forwards;-o-animation:fade-in.3s ease-out forwards;animation:fade-in.3s ease-out forwards}.animate-float.jsx-3e1eea4381e0e46b{will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-fade-in.jsx-3e1eea4381e0e46b{will-change:transform,opacity;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/register/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLDREQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUM5QyxHQUFHQyxLQUFLOzs7Ozs7QUFHYkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUcsZ0NBQWtCWCw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JNLGdCQUFnQkgsV0FBVyxHQUFHO0FBRTlCLE1BQU1JLDRCQUFjWiw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFTyxZQUFZSixXQUFXLEdBQUc7QUFFMUIsTUFBTUssMkJBQWFiLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlEsV0FBV0wsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJyb3VuZGVkLWxnIGJvcmRlciBiZy1jYXJkIHRleHQtY2FyZC1mb3JlZ3JvdW5kIHNoYWRvdy1zbVwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZC5kaXNwbGF5TmFtZSA9IFwiQ2FyZFwiXG5cbmNvbnN0IENhcmRIZWFkZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEhlYWRlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEhlYWRlclwiXG5cbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbGVhZGluZy1ub25lIHRyYWNraW5nLXRpZ2h0XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkVGl0bGUuZGlzcGxheU5hbWUgPSBcIkNhcmRUaXRsZVwiXG5cbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSBcIkNhcmREZXNjcmlwdGlvblwiXG5cbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwicC02IHB0LTBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gXCJDYXJkQ29udGVudFwiXG5cbmNvbnN0IENhcmRGb290ZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBpdGVtcy1jZW50ZXIgcC02IHB0LTBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRGb290ZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/checkbox.tsx":
/*!************************************!*\
  !*** ./components/ui/checkbox.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-checkbox */ \"(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Checkbox auto */ \n\n\n\n\nconst Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center justify-center text-current\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\checkbox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\checkbox.tsx\",\n            lineNumber: 21,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\checkbox.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined));\nCheckbox.displayName = _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/checkbox.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-email-verification.ts":
/*!*****************************************!*\
  !*** ./hooks/use-email-verification.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEmailVerification: () => (/* binding */ useEmailVerification)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-service */ \"(ssr)/./lib/auth-service.ts\");\n// 邮箱验证相关的React Hook\n\n\nfunction useEmailVerification(options = {}) {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isSending: false,\n        sendError: null,\n        isCodeSent: false,\n        isVerifying: false,\n        verifyError: null,\n        countdown: 0,\n        canResend: true,\n        pendingRegistration: null\n    });\n    // 倒计时效果\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEmailVerification.useEffect\": ()=>{\n            let timer;\n            if (state.countdown > 0) {\n                timer = setTimeout({\n                    \"useEmailVerification.useEffect\": ()=>{\n                        setState({\n                            \"useEmailVerification.useEffect\": (prev)=>({\n                                    ...prev,\n                                    countdown: prev.countdown - 1,\n                                    canResend: prev.countdown <= 1\n                                })\n                        }[\"useEmailVerification.useEffect\"]);\n                    }\n                }[\"useEmailVerification.useEffect\"], 1000);\n            }\n            return ({\n                \"useEmailVerification.useEffect\": ()=>{\n                    if (timer) clearTimeout(timer);\n                }\n            })[\"useEmailVerification.useEffect\"];\n        }\n    }[\"useEmailVerification.useEffect\"], [\n        state.countdown\n    ]);\n    // 发送验证码\n    const sendVerificationCode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[sendVerificationCode]\": async (data)=>{\n            setState({\n                \"useEmailVerification.useCallback[sendVerificationCode]\": (prev)=>({\n                        ...prev,\n                        isSending: true,\n                        sendError: null\n                    })\n            }[\"useEmailVerification.useCallback[sendVerificationCode]\"]);\n            try {\n                const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_1__.auth.sendVerificationCode(data);\n                setState({\n                    \"useEmailVerification.useCallback[sendVerificationCode]\": (prev)=>({\n                            ...prev,\n                            isSending: false,\n                            isCodeSent: true,\n                            countdown: 60,\n                            canResend: false,\n                            pendingRegistration: {\n                                email: data.email,\n                                username: data.username,\n                                password: data.password\n                            }\n                        })\n                }[\"useEmailVerification.useCallback[sendVerificationCode]\"]);\n                return response;\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : '发送验证码失败';\n                setState({\n                    \"useEmailVerification.useCallback[sendVerificationCode]\": (prev)=>({\n                            ...prev,\n                            isSending: false,\n                            sendError: errorMessage\n                        })\n                }[\"useEmailVerification.useCallback[sendVerificationCode]\"]);\n                options.onError?.(errorMessage);\n                throw error;\n            }\n        }\n    }[\"useEmailVerification.useCallback[sendVerificationCode]\"], [\n        options\n    ]);\n    // 重新发送验证码\n    const resendVerificationCode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[resendVerificationCode]\": async ()=>{\n            if (!state.canResend || !state.pendingRegistration) {\n                return;\n            }\n            return await sendVerificationCode(state.pendingRegistration);\n        }\n    }[\"useEmailVerification.useCallback[resendVerificationCode]\"], [\n        state.canResend,\n        state.pendingRegistration,\n        sendVerificationCode\n    ]);\n    // 验证邮箱并完成注册\n    const verifyEmailAndRegister = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[verifyEmailAndRegister]\": async (code)=>{\n            if (!state.pendingRegistration) {\n                throw new Error('没有待验证的注册信息');\n            }\n            setState({\n                \"useEmailVerification.useCallback[verifyEmailAndRegister]\": (prev)=>({\n                        ...prev,\n                        isVerifying: true,\n                        verifyError: null\n                    })\n            }[\"useEmailVerification.useCallback[verifyEmailAndRegister]\"]);\n            try {\n                const verifyData = {\n                    username: state.pendingRegistration.username,\n                    email: state.pendingRegistration.email,\n                    code: code.trim()\n                };\n                const response = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_1__.auth.verifyEmailAndRegister(verifyData);\n                setState({\n                    \"useEmailVerification.useCallback[verifyEmailAndRegister]\": (prev)=>({\n                            ...prev,\n                            isVerifying: false,\n                            pendingRegistration: null\n                        })\n                }[\"useEmailVerification.useCallback[verifyEmailAndRegister]\"]);\n                options.onSuccess?.();\n                return response;\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : '验证失败';\n                setState({\n                    \"useEmailVerification.useCallback[verifyEmailAndRegister]\": (prev)=>({\n                            ...prev,\n                            isVerifying: false,\n                            verifyError: errorMessage\n                        })\n                }[\"useEmailVerification.useCallback[verifyEmailAndRegister]\"]);\n                options.onError?.(errorMessage);\n                throw error;\n            }\n        }\n    }[\"useEmailVerification.useCallback[verifyEmailAndRegister]\"], [\n        state.pendingRegistration,\n        options\n    ]);\n    // 清除错误\n    const clearErrors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[clearErrors]\": ()=>{\n            setState({\n                \"useEmailVerification.useCallback[clearErrors]\": (prev)=>({\n                        ...prev,\n                        sendError: null,\n                        verifyError: null\n                    })\n            }[\"useEmailVerification.useCallback[clearErrors]\"]);\n        }\n    }[\"useEmailVerification.useCallback[clearErrors]\"], []);\n    // 重置状态\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[reset]\": ()=>{\n            setState({\n                isSending: false,\n                sendError: null,\n                isCodeSent: false,\n                isVerifying: false,\n                verifyError: null,\n                countdown: 0,\n                canResend: true,\n                pendingRegistration: null\n            });\n        }\n    }[\"useEmailVerification.useCallback[reset]\"], []);\n    // 格式化倒计时显示\n    const getCountdownText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEmailVerification.useCallback[getCountdownText]\": ()=>{\n            if (state.countdown > 0) {\n                return `${state.countdown}秒后可重新发送`;\n            }\n            return '重新发送验证码';\n        }\n    }[\"useEmailVerification.useCallback[getCountdownText]\"], [\n        state.countdown\n    ]);\n    return {\n        // 状态\n        ...state,\n        // 方法\n        sendVerificationCode,\n        resendVerificationCode,\n        verifyEmailAndRegister,\n        clearErrors,\n        reset,\n        // 辅助方法\n        getCountdownText,\n        // 计算属性\n        hasError: !!(state.sendError || state.verifyError),\n        isLoading: state.isSending || state.isVerifying\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-email-verification.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n// API配置和服务层\n// 兼容 Cloudflare Pages 部署\n// API配置\nconst API_CONFIG = {\n    // 生产环境使用你的 Cloudflare Worker 域名\n    BASE_URL: \"http://localhost:3001\" || 0,\n    // 备用API配置 - 支持多个备用API（逗号分隔）\n    BACKUP_URL: \"http://localhost:3001\" || 0,\n    BACKUP_URLS:  true ? \"http://localhost:3001\".split(',').map((url)=>url.trim()).filter((url)=>url.length > 0) : 0,\n    ENABLE_BACKUP: \"false\" === 'true',\n    TIMEOUT: 30000\n};\n// API端点定义\nconst API_ENDPOINTS = {\n    // 认证相关\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        REGISTER: '/api/auth/register',\n        REFRESH: '/api/auth/refresh',\n        SEND_VERIFICATION: '/api/auth/send-verification',\n        VERIFY_EMAIL: '/api/auth/verify-email',\n        CHANGE_PASSWORD: '/api/auth/change-password',\n        FORGOT_PASSWORD: '/api/auth/forgot-password',\n        RESET_PASSWORD: '/api/auth/reset-password'\n    },\n    // TTS相关\n    TTS: {\n        GENERATE: '/api/tts/generate',\n        STATUS: '/api/tts/status',\n        STREAM: '/api/tts/stream',\n        DOWNLOAD: '/api/tts/download'\n    },\n    // 用户相关\n    USER: {\n        QUOTA: '/api/user/quota'\n    },\n    // 卡密相关\n    CARD: {\n        USE: '/api/card/use'\n    },\n    // 自动标注相关\n    AUTO_TAG: {\n        PROCESS: '/api/auto-tag/process',\n        STATUS: '/api/auto-tag/status',\n        ADMIN_STATS: '/api/auto-tag/admin/stats'\n    }\n};\n// Token管理\nclass TokenManager {\n    static{\n        this.ACCESS_TOKEN_KEY = 'access_token';\n    }\n    static{\n        this.REFRESH_TOKEN_KEY = 'refresh_token';\n    }\n    static{\n        this.USER_EMAIL_KEY = 'userEmail';\n    }\n    static getAccessToken() {\n        if (true) return null;\n        return localStorage.getItem(this.ACCESS_TOKEN_KEY);\n    }\n    static getRefreshToken() {\n        if (true) return null;\n        return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n    }\n    static getUserEmail() {\n        if (true) return null;\n        return localStorage.getItem(this.USER_EMAIL_KEY);\n    }\n    static setTokens(accessToken, refreshToken, email) {\n        if (true) return;\n        localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);\n        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);\n        if (email) {\n            localStorage.setItem(this.USER_EMAIL_KEY, email);\n        }\n        // 保持兼容性\n        localStorage.setItem('isLoggedIn', 'true');\n    }\n    static clearTokens() {\n        if (true) return;\n        localStorage.removeItem(this.ACCESS_TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n        localStorage.removeItem(this.USER_EMAIL_KEY);\n        // 保持兼容性\n        localStorage.removeItem('isLoggedIn');\n    }\n    static isLoggedIn() {\n        return !!this.getAccessToken();\n    }\n}\n// HTTP客户端类\nclass ApiClient {\n    constructor(){\n        this.baseURL = API_CONFIG.BASE_URL;\n        this.backupURL = API_CONFIG.BACKUP_URL;\n        this.backupURLs = API_CONFIG.BACKUP_URLS;\n        this.enableBackup = API_CONFIG.ENABLE_BACKUP;\n        this.timeout = API_CONFIG.TIMEOUT;\n    }\n    // 获取当前可用的API URL（主要用于外部访问）\n    getCurrentApiUrl(useBackup = false, backupIndex = 0) {\n        if (useBackup && this.enableBackup) {\n            // 优先使用多个备用API配置\n            if (this.backupURLs.length > 0 && backupIndex >= 0 && backupIndex < this.backupURLs.length) {\n                return this.backupURLs[backupIndex];\n            }\n            // 向后兼容：使用单个备用API配置\n            if (this.backupURL) {\n                return this.backupURL;\n            }\n        }\n        return this.baseURL;\n    }\n    // 检查备用API是否可用\n    isBackupApiAvailable() {\n        return this.enableBackup && (this.backupURLs.length > 0 || !!this.backupURL);\n    }\n    // 获取备用API数量\n    getBackupApiCount() {\n        if (!this.enableBackup) return 0;\n        return this.backupURLs.length > 0 ? this.backupURLs.length : this.backupURL ? 1 : 0;\n    }\n    // 获取指定索引的备用API URL\n    getBackupApiUrl(index) {\n        if (!this.enableBackup) return null;\n        // 优先使用多个备用API配置\n        if (this.backupURLs.length > 0) {\n            return index >= 0 && index < this.backupURLs.length ? this.backupURLs[index] : null;\n        }\n        // 向后兼容：使用单个备用API配置\n        return index === 0 && this.backupURL ? this.backupURL : null;\n    }\n    // 创建请求头\n    createHeaders(includeAuth = false) {\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        if (includeAuth) {\n            const token = TokenManager.getAccessToken();\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n        }\n        return headers;\n    }\n    // 处理响应\n    async handleResponse(response) {\n        if (!response.ok) {\n            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;\n            let errorCode = null;\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.error || errorData.message || errorMessage;\n                errorCode = errorData.code || null // 【新增】提取错误码\n                ;\n            } catch  {\n            // 如果无法解析JSON，使用默认错误消息\n            }\n            // 【新增】创建带有错误码的自定义错误对象\n            const error = new Error(errorMessage);\n            if (errorCode) {\n                error.code = errorCode;\n            }\n            throw error;\n        }\n        return await response.json();\n    }\n    // 通用请求方法\n    async request(endpoint, options = {}, includeAuth = false) {\n        const url = `${this.baseURL}${endpoint}`;\n        const headers = this.createHeaders(includeAuth);\n        const config = {\n            ...options,\n            headers: {\n                ...headers,\n                ...options.headers\n            }\n        };\n        // 创建AbortController用于超时控制\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url, {\n                ...config,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return await this.handleResponse(response);\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    throw new Error('请求超时，请检查网络连接');\n                }\n                throw error;\n            }\n            throw new Error('网络请求失败');\n        }\n    }\n    // GET请求\n    async get(endpoint, includeAuth = false) {\n        return this.request(endpoint, {\n            method: 'GET'\n        }, includeAuth);\n    }\n    // POST请求\n    async post(endpoint, data, includeAuth = false) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // PUT请求\n    async put(endpoint, data, includeAuth = false) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // DELETE请求\n    async delete(endpoint, includeAuth = false) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        }, includeAuth);\n    }\n}\n// 创建全局API客户端实例\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-service.ts":
/*!*****************************!*\
  !*** ./lib/auth-service.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   AutoTagService: () => (/* binding */ AutoTagService),\n/* harmony export */   CardService: () => (/* binding */ CardService),\n/* harmony export */   TTSService: () => (/* binding */ TTSService),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   autoTagService: () => (/* binding */ autoTagService),\n/* harmony export */   cardService: () => (/* binding */ cardService),\n/* harmony export */   ttsService: () => (/* binding */ ttsService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./lib/api.ts\");\n// 认证服务\n// 专门处理认证相关的API调用\n\nclass AuthService {\n    // 登录\n    static async login(credentials) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.LOGIN, credentials);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, credentials.username);\n            return response;\n        } catch (error) {\n            console.error('Login error:', error);\n            throw error;\n        }\n    }\n    // 传统注册（保持兼容性）\n    static async register(userData) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.REGISTER, userData);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, userData.username);\n            return response;\n        } catch (error) {\n            console.error('Register error:', error);\n            throw error;\n        }\n    }\n    // 发送邮箱验证码\n    static async sendVerificationCode(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.SEND_VERIFICATION, data);\n            return response;\n        } catch (error) {\n            console.error('Send verification error:', error);\n            throw error;\n        }\n    }\n    // 验证邮箱并完成注册\n    static async verifyEmailAndRegister(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.VERIFY_EMAIL, data);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, data.email);\n            return response;\n        } catch (error) {\n            console.error('Verify email error:', error);\n            throw error;\n        }\n    }\n    // 刷新token\n    static async refreshToken() {\n        try {\n            const refreshToken = _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getRefreshToken();\n            if (!refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.REFRESH, {\n                refresh_token: refreshToken\n            });\n            // 更新token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token);\n            return response;\n        } catch (error) {\n            console.error('Refresh token error:', error);\n            // 如果刷新失败，清除所有token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n            throw error;\n        }\n    }\n    // 登出\n    static async logout() {\n        try {\n            // 清除本地token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n        // 可以在这里添加服务端登出逻辑\n        // await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT, {}, true)\n        } catch (error) {\n            console.error('Logout error:', error);\n            // 即使服务端登出失败，也要清除本地token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n        }\n    }\n    // 获取用户配额信息\n    static async getUserQuota() {\n        try {\n            return await this.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.USER.QUOTA, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Get user quota error:', error);\n            throw error;\n        }\n    }\n    // 修改密码\n    static async changePassword(data) {\n        try {\n            return await this.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Change password error:', error);\n            throw error;\n        }\n    }\n    // 忘记密码 - 发送重置验证码\n    static async forgotPassword(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.FORGOT_PASSWORD, data);\n            return response;\n        } catch (error) {\n            console.error('Forgot password error:', error);\n            throw error;\n        }\n    }\n    // 重置密码\n    static async resetPassword(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.RESET_PASSWORD, data);\n            return response;\n        } catch (error) {\n            console.error('Reset password error:', error);\n            throw error;\n        }\n    }\n    // 检查登录状态\n    static isLoggedIn() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.isLoggedIn();\n    }\n    // 获取当前用户邮箱\n    static getCurrentUserEmail() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getUserEmail();\n    }\n    // 【新增】判断是否为认证错误的辅助函数\n    static isAuthError(error) {\n        // 优先检查错误码（更可靠）\n        if (error.code) {\n            return error.code === 'TOKEN_EXPIRED' || error.code === 'TOKEN_INVALID' || error.code === 'TOKEN_TYPE_INVALID' || error.code === 'NO_TOKEN';\n        }\n        // 兼容旧版：检查错误消息\n        if (error.message) {\n            return error.message.includes('401') || error.message.toLowerCase().includes('token') || error.message.toLowerCase().includes('expired') || error.message.includes('登录') || error.message.includes('unauthorized');\n        }\n        return false;\n    }\n    // 自动刷新token的包装器\n    static async withTokenRefresh(apiCall) {\n        try {\n            return await apiCall();\n        } catch (error) {\n            // 【关键修改】使用新的认证错误判断逻辑\n            if (this.isAuthError(error)) {\n                // Token可能过期，尝试刷新\n                try {\n                    await this.refreshToken();\n                    // 重试原始请求\n                    return await apiCall();\n                } catch (refreshError) {\n                    // 刷新失败，清理本地数据\n                    this.logout();\n                    // 【修复】创建一个特殊的认证失败错误，让调用方处理UI和跳转\n                    const authFailedError = new Error('Authentication failed - refresh token expired');\n                    authFailedError.code = 'REFRESH_TOKEN_EXPIRED';\n                    authFailedError.shouldRedirect = true;\n                    throw authFailedError;\n                }\n            }\n            throw error;\n        }\n    }\n}\n// 卡密服务\nclass CardService {\n    // 使用卡密充值\n    static async useCard(code) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CARD.USE, {\n                    code\n                }, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Use card error:', error);\n            throw error;\n        }\n    }\n}\n// TTS服务\nclass TTSService {\n    // 原有的同步生成语音方法（保持向后兼容）\n    static async generateSpeech(data) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(`${_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL}${_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.GENERATE}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken()}`\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    const error = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n                    // 传递错误类型信息\n                    if (errorData.type) {\n                        error.type = errorData.type;\n                    }\n                    throw error;\n                }\n                return await response.arrayBuffer();\n            });\n        } catch (error) {\n            console.error('Generate speech error:', error);\n            throw error;\n        }\n    }\n    // 新的异步任务启动方法\n    static async startAsyncGeneration(data) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(`${_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL}${_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.GENERATE}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken()}`\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    const error = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n                    // 传递错误类型信息\n                    if (errorData.type) {\n                        error.type = errorData.type;\n                    }\n                    throw error;\n                }\n                return await response.json();\n            });\n        } catch (error) {\n            console.error('Start async generation error:', error);\n            throw error;\n        }\n    }\n    // 检查任务状态\n    static async checkTaskStatus(taskId) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(`${_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL}${_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.STATUS}/${taskId}`, {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': `Bearer ${_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken()}`\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n                }\n                return await response.json();\n            });\n        } catch (error) {\n            console.error('Check task status error:', error);\n            throw error;\n        }\n    }\n    // 下载完成的音频 - Worker代理方式（备用）\n    static async downloadAudio(taskId) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(`${_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL}${_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.DOWNLOAD}/${taskId}`, {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': `Bearer ${_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken()}`\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n                }\n                return await response.arrayBuffer();\n            });\n        } catch (error) {\n            console.error('Download audio error:', error);\n            throw error;\n        }\n    }\n    // R2直链下载方式（优化后）\n    static async downloadFromDirectUrl(directUrl) {\n        try {\n            // 检查是否为开发环境且存在CORS问题\n            const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n            let fetchOptions = {\n                method: 'GET'\n            };\n            // 开发环境CORS处理\n            if (isDevelopment) {\n                // 尝试直接访问，如果CORS失败则通过代理\n                try {\n                    await fetch(directUrl, {\n                        method: 'HEAD',\n                        mode: 'cors'\n                    });\n                } catch (corsError) {\n                    fetchOptions.mode = 'no-cors';\n                    // 如果no-cors也不行，则抛出错误让其回退到Worker代理\n                    if (corsError instanceof TypeError && corsError.message.includes('CORS')) {\n                        throw new Error(`CORS_ERROR: ${corsError.message}`);\n                    }\n                }\n            }\n            const response = await fetch(directUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(`R2 direct download failed: HTTP ${response.status}: ${response.statusText}`);\n            }\n            const arrayBuffer = await response.arrayBuffer();\n            return arrayBuffer;\n        } catch (error) {\n            // 如果是CORS错误，提供更详细的错误信息\n            if (error instanceof Error && error.message.includes('CORS')) {\n                throw new Error(`R2_CORS_ERROR: CORS configuration needed for r2-assets.aispeak.top. ${error.message}`);\n            }\n            throw error;\n        }\n    }\n    static{\n        // 防重复下载缓存\n        this.downloadCache = new Map();\n    }\n    // 智能轮询任务状态直到完成\n    static async pollTaskUntilComplete(taskId, onProgress, maxAttempts = 60, initialDelay = 2000) {\n        // 检查是否已经在下载中\n        if (this.downloadCache.has(taskId)) {\n            return await this.downloadCache.get(taskId);\n        }\n        // 创建下载Promise并缓存\n        const downloadPromise = this.performPolling(taskId, onProgress, maxAttempts, initialDelay);\n        this.downloadCache.set(taskId, downloadPromise);\n        try {\n            const result = await downloadPromise;\n            // 下载完成后清理缓存\n            this.downloadCache.delete(taskId);\n            return result;\n        } catch (error) {\n            // 下载失败也要清理缓存\n            this.downloadCache.delete(taskId);\n            throw error;\n        }\n    }\n    // 实际的轮询逻辑\n    static async performPolling(taskId, onProgress, maxAttempts = 60, initialDelay = 2000) {\n        let attempts = 0;\n        let delay = initialDelay;\n        while(attempts < maxAttempts){\n            try {\n                const status = await this.checkTaskStatus(taskId);\n                // 调用进度回调\n                if (onProgress) {\n                    onProgress(status);\n                }\n                if (status.status === 'complete') {\n                    // 任务完成，智能选择下载方式\n                    // 优先使用R2直链下载\n                    if (status.audioUrl && status.audioUrl.includes('r2-assets.aispeak.top')) {\n                        try {\n                            return await this.downloadFromDirectUrl(status.audioUrl);\n                        } catch (r2Error) {\n                            // R2直链失败，回退到Worker代理下载\n                            return await this.downloadAudio(taskId);\n                        }\n                    } else {\n                        // 没有R2直链或不是R2直链，使用Worker代理下载\n                        return await this.downloadAudio(taskId);\n                    }\n                } else if (status.status === 'failed') {\n                    // 任务失败\n                    throw new Error(status.error || 'Task failed');\n                }\n                // 任务仍在处理中，等待后重试\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // 指数退避：延迟时间逐渐增加，但不超过10秒\n                delay = Math.min(delay * 1.2, 10000);\n                attempts++;\n            } catch (error) {\n                console.error(`Polling attempt ${attempts + 1} failed:`, error);\n                attempts++;\n                // 如果是网络错误，稍等后重试\n                if (attempts < maxAttempts) {\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    delay = Math.min(delay * 1.5, 10000);\n                } else {\n                    throw error;\n                }\n            }\n        }\n        throw new Error('Task polling timeout - maximum attempts reached');\n    }\n}\n// 自动标注服务\nclass AutoTagService {\n    // 处理自动标注请求\n    static async processText(text, language = 'auto') {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTO_TAG.PROCESS, {\n                    text,\n                    language\n                }, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Auto tag process error:', error);\n            throw error;\n        }\n    }\n    // 获取使用状态\n    static async getStatus() {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTO_TAG.STATUS, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Auto tag status error:', error);\n            throw error;\n        }\n    }\n}\n// 导出便捷方法\nconst auth = AuthService;\nconst cardService = CardService;\nconst ttsService = TTSService;\nconst autoTagService = AutoTagService;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteWFpdHRzJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbXlhaXR0cyU1QyU1Q2Zyb250ZW5kJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDdG9hc3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJEOlxcXFxteWFpdHRzXFxcXGZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcdG9hc3Rlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/register/page.tsx */ \"(ssr)/./app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteWFpdHRzJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNyZWdpc3RlciU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBbUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15YWl0dHNcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHJlZ2lzdGVyXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=D%3A%5Cmyaitts%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmyaitts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();