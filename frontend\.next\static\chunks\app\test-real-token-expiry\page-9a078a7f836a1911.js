(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[154],{3580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f});var a=s(2115);let n=0,o=new Map,r=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?r(s):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e,s=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=a.useState(c);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4265:(e,t,s)=>{Promise.resolve().then(s.bind(s,7363))},4835:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7363:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(5155),n=s(2115),o=s(7168),r=s(8482),i=s(9840),l=s(6194),c=s(1886),d=s(3580),u=s(7492),f=s(4186),p=s(4835);function g(){let[e,t]=(0,n.useState)(""),[s,g]=(0,n.useState)(!1),{toast:m}=(0,d.dj)(),h=()=>{let e=btoa(JSON.stringify({alg:"HS256",typ:"JWT"})),t=btoa(JSON.stringify({sub:"<EMAIL>",type:"access",iat:Date.now()-72e5,exp:Date.now()-36e5})),s=btoa("fake_signature_for_testing");return"".concat(e,".").concat(t,".").concat(s)},x=()=>{let e=btoa(JSON.stringify({alg:"HS256",typ:"JWT"})),t=btoa(JSON.stringify({sub:"<EMAIL>",type:"refresh",iat:Date.now()-72e5,exp:Date.now()-36e5})),s=btoa("fake_refresh_signature_for_testing");return"".concat(e,".").concat(t,".").concat(s)},N=async()=>{try{t("开始获取用户状态...");let e=await l.j2.getUserQuota();t("成功获取用户状态: "+JSON.stringify(e,null,2))}catch(a){console.error("获取用户状态失败:",a),t("获取用户状态失败: ".concat(a.message,"\n错误码: ").concat(a.code||"无","\nshouldRedirect: ").concat(a.shouldRedirect||"无"));let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(a,()=>{console.log("认证错误回调被触发，显示弹窗"),g(!0)});console.log("错误处理结果:",{isAuth:e,shouldRedirect:s}),e&&s&&(console.log("将在2秒后跳转到登录页面"),setTimeout(()=>{console.log("执行跳转到登录页面"),window.location.href="/login"},2e3))}};return(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsxs)(r.Zp,{className:"max-w-3xl mx-auto",children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"真实Token过期测试"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"测试真正过期的JWT token vs 无效字符串的区别"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.$,{onClick:()=>{let e=c.tC.getAccessToken(),s=c.tC.getRefreshToken(),a="无",n="无";if(e)try{let t=e.split(".");if(3===t.length){let s=JSON.parse(atob(t[1])),n=Date.now()>s.exp;a="".concat(e.substring(0,20),"... (过期: ").concat(n?"是":"否",", 过期时间: ").concat(new Date(s.exp).toLocaleString(),")")}else a="".concat(e.substring(0,20),"... (格式: 无效)")}catch(t){a="".concat(e.substring(0,20),"... (格式: 无效)")}if(s)try{let e=s.split(".");if(3===e.length){let t=JSON.parse(atob(e[1])),a=Date.now()>t.exp;n="".concat(s.substring(0,20),"... (过期: ").concat(a?"是":"否",", 过期时间: ").concat(new Date(t.exp).toLocaleString(),")")}else n="".concat(s.substring(0,20),"... (格式: 无效)")}catch(e){n="".concat(s.substring(0,20),"... (格式: 无效)")}t("当前tokens状态:\nAccess Token: ".concat(a,"\nRefresh Token: ").concat(n))},variant:"outline",children:"检查当前Tokens详情"}),(0,a.jsx)(o.$,{onClick:()=>{let e=h(),s=x();c.tC.setTokens(e,s,"<EMAIL>"),t("已设置真正过期的JWT tokens:\nAccess Token (前50字符): ".concat(e.substring(0,50),"...\nRefresh Token (前50字符): ").concat(s.substring(0,50),"...\n\n现在可以测试fetchUserStatus，应该能正确识别为TOKEN_EXPIRED"))},variant:"secondary",children:"设置真正过期的JWT Tokens"}),(0,a.jsx)(o.$,{onClick:()=>{c.tC.setTokens("invalid_access_token","invalid_refresh_token","<EMAIL>"),t("已设置无效的tokens（对比测试），现在可以测试fetchUserStatus")},variant:"secondary",children:"设置无效字符串Tokens（对比）"}),(0,a.jsx)(o.$,{onClick:N,variant:"default",children:"测试fetchUserStatus"}),(0,a.jsx)(o.$,{onClick:()=>{c.tC.clearTokens(),t("已清除所有tokens"),g(!1)},variant:"destructive",children:"清除所有Tokens"})]}),e&&(0,a.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,a.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]})]})]}),(0,a.jsx)(i.lG,{open:s,onOpenChange:g,children:(0,a.jsxs)(i.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,a.jsxs)(i.c7,{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"w-8 h-8 text-blue-500"})}),(0,a.jsx)(i.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,a.jsx)(i.rr,{className:"text-gray-600",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,a.jsx)("span",{className:"text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,a.jsx)(i.Es,{className:"mt-6",children:(0,a.jsxs)(o.$,{onClick:async()=>{g(!1),await l.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})})]})}},7492:(e,t,s)=>{"use strict";s.d(t,{A:()=>c,tu:()=>l,zf:()=>i});let a={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"},n=[/[A-Za-z]:\\[\w\\.-]+/g,/\/[\w\/.-]+\.(js|ts|json|sql|env)/g,/node_modules/gi,/at\s+[\w.]+\s+\(/g,/https?:\/\/[\w.-]+\/[\w\/.-]*/g,/localhost:\d+/g,/\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,/table\s+["']?\w+["']?/gi,/column\s+["']?\w+["']?/gi,/constraint\s+["']?\w+["']?/gi,/error:\s*\w+error/gi,/errno\s*:\s*\d+/gi,/api[_-]?key/gi,/secret/gi,/password/gi],o={network:"网络连接异常，请检查网络后重试",timeout:"请求超时，请稍后重试",server:"服务器暂时不可用，请稍后再试",database:"数据处理异常，请稍后重试",auth:"认证失败，请重新登录",unknown:"发生未知错误，请稍后重试",default:"操作失败，请稍后重试"};function r(e){if(null==e?void 0:e.code)return Object.values(a).includes(e.code);if(null==e?void 0:e.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function i(e){let t="";return(t=(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error)?e.error:"string"==typeof e?e:"操作失败，请稍后重试",r(e))?"登录会话已过期，请重新登录":function(e){if(!e||"string"!=typeof e)return o.default;if(e&&"string"==typeof e&&n.some(t=>t.test(e))){let t=e.toLowerCase();if(t.includes("network")||t.includes("fetch"))return o.network;if(t.includes("timeout"))return o.timeout;if(t.includes("auth")||t.includes("token"))return o.auth;else if(t.includes("server")||t.includes("internal"))return o.server;else if(t.includes("database"))return o.database;else return o.unknown}return e}(t)}function l(e,t){let s=r(e);s&&t&&t();let a=r(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:i(e),shouldRedirect:!1};return{isAuthError:s,message:a.description,shouldRedirect:a.shouldRedirect}}function c(e){var t;return r(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(t=e.message)||void 0===t?void 0:t.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:i(e),variant:"destructive"}}},9840:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>p,L3:()=>g,c7:()=>f,lG:()=>l,rr:()=>m});var a=s(5155),n=s(2115),o=s(3651),r=s(4416),i=s(3999);let l=o.bL;o.l9;let c=o.ZL;o.bm;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(o.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n})});d.displayName=o.hJ.displayName;let u=n.forwardRef((e,t)=>{let{className:s,children:n,...l}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(d,{}),(0,a.jsxs)(o.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l,children:[n,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=o.UC.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};f.displayName="DialogHeader";let p=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};p.displayName="DialogFooter";let g=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(o.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...n})});g.displayName=o.hE.displayName;let m=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(o.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...n})});m.displayName=o.VY.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[352,150,651,576,441,684,358],()=>t(4265)),_N_E=e.O()}]);